[2025-09-01T16:53:50.946+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T16:53:50.946+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T16:53:50.946+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T16:53:50.946+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.046+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.046+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:22:26.047+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.047+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.047+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:22:26.047+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:22:26.047+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:22:26.048+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:22:26.048+08:00] provider has no specific model SDK declared
[2025-09-01T17:22:26.048+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.360+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:23:44.361+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.361+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.361+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.361+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:23:44.361+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.361+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.361+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.361+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.361+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:23:44.362+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.362+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.362+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:23:44.362+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:23:44.362+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:23:44.362+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:23:44.362+08:00] provider has no specific model SDK declared
[2025-09-01T17:23:44.362+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.006+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.006+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:26:13.006+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.006+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:26:13.007+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:26:13.007+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:26:13.007+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:26:13.007+08:00] provider has no specific model SDK declared
[2025-09-01T17:26:13.007+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.477+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.477+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.478+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:27:13.478+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:27:13.478+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:27:13.478+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:27:13.478+08:00] provider has no specific model SDK declared
[2025-09-01T17:27:13.479+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.968+08:00] Get value of MIDSCENE_VQA_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.968+08:00] decideModelConfig as legacy logic with intent VQA.
[2025-09-01T17:28:22.969+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.969+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.969+08:00] decideModelConfig result by legacy logic with intent VQA: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.969+08:00] Get value of MIDSCENE_MODEL_NAME from globalConfig doubao-1-5-ui-tars-250428
[2025-09-01T17:28:22.969+08:00] decideModelConfig as legacy logic with intent default.
[2025-09-01T17:28:22.969+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent default: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.970+08:00] Get value of MIDSCENE_GROUNDING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.970+08:00] decideModelConfig as legacy logic with intent grounding.
[2025-09-01T17:28:22.970+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent grounding: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
[2025-09-01T17:28:22.970+08:00] Get value of MIDSCENE_PLANNING_MODEL_NAME from globalConfig undefined
[2025-09-01T17:28:22.970+08:00] decideModelConfig as legacy logic with intent planning.
[2025-09-01T17:28:22.970+08:00] enter decideOpenaiSdkConfig with keys: {
  modelName: 'MIDSCENE_MODEL_NAME',
  socksProxy: 'MIDSCENE_OPENAI_SOCKS_PROXY',
  httpProxy: 'MIDSCENE_OPENAI_HTTP_PROXY',
  openaiBaseURL: 'OPENAI_BASE_URL',
  openaiApiKey: 'OPENAI_API_KEY',
  openaiExtraConfig: 'MIDSCENE_OPENAI_INIT_CONFIG_JSON',
  openaiUseAzureDeprecated: 'OPENAI_USE_AZURE',
  useAzureOpenai: 'MIDSCENE_USE_AZURE_OPENAI',
  azureOpenaiScope: 'MIDSCENE_AZURE_OPENAI_SCOPE',
  azureOpenaiKey: 'AZURE_OPENAI_KEY',
  azureOpenaiEndpoint: 'AZURE_OPENAI_ENDPOINT',
  azureOpenaiApiVersion: 'AZURE_OPENAI_API_VERSION',
  azureOpenaiDeployment: 'AZURE_OPENAI_DEPLOYMENT',
  azureExtraConfig: 'MIDSCENE_AZURE_OPENAI_INIT_CONFIG_JSON',
  useAnthropicSdk: 'MIDSCENE_USE_ANTHROPIC_SDK',
  anthropicApiKey: 'ANTHROPIC_API_KEY',
  vlMode: 'DEFAULT_MODEL_CONFIG_KEYS has no vlMode key'
}
[2025-09-01T17:28:22.970+08:00] provider has no specific model SDK declared
[2025-09-01T17:28:22.970+08:00] decideModelConfig result by legacy logic with intent planning: {
  socksProxy: undefined,
  httpProxy: undefined,
  vlModeRaw: undefined,
  openaiBaseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  openaiApiKey: '7cd******************************eb2',
  openaiExtraConfig: undefined,
  modelName: 'doubao-1-5-ui-tars-250428',
  vlMode: 'vlm-ui-tars',
  uiTarsVersion: '1.5',
  modelDescription: 'UI-TARS=1.5',
  from: 'legacy-env'
}
