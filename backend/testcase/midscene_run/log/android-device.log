[2025-09-01T17:22:26.049+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-09-01T17:22:26.050+08:00] adb shell wm,size
[2025-09-01T17:22:26.195+08:00] adb shell wm,size end
[2025-09-01T17:22:26.195+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:26.195+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:26.277+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: <PERSON><PERSON><PERSON> executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:26.277+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:26.277+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:26.386+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:26.387+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:26.387+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-09-01T17:22:26.387+08:00] Launching app: com.transsion.aivoiceassistant
[2025-09-01T17:22:26.387+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-09-01T17:22:26.727+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-09-01T17:22:26.727+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-09-01T17:22:26.803+08:00] screenshotBase64 begin
[2025-09-01T17:22:26.803+08:00] adb shell wm,size
[2025-09-01T17:22:27.051+08:00] adb shell wm,size end
[2025-09-01T17:22:27.051+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:27.051+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:27.267+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:27.268+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:27.268+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:27.652+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:27.652+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:27.653+08:00] adb getScreenDensity 
[2025-09-01T17:22:27.768+08:00] adb getScreenDensity  end
[2025-09-01T17:22:27.769+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:27.769+08:00] adb takeScreenshot 
[2025-09-01T17:22:28.065+08:00] adb takeScreenshot  end
[2025-09-01T17:22:28.065+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:28.065+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:28.065+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:28.066+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:28.067+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png
[2025-09-01T17:22:28.830+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png end
[2025-09-01T17:22:28.830+08:00] adb.shell screencap completed
[2025-09-01T17:22:28.830+08:00] Pulling screenshot file from device
[2025-09-01T17:22:28.830+08:00] adb pull /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3nd4i90x6k9.png
[2025-09-01T17:22:28.897+08:00] adb pull /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3nd4i90x6k9.png end
[2025-09-01T17:22:28.898+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3nd4i90x6k9.png
[2025-09-01T17:22:28.899+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png
[2025-09-01T17:22:28.958+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fc2cc18a-bc13-4ddf-b3bd-b3a157fc4f3f.png end
[2025-09-01T17:22:28.958+08:00] Resizing screenshot image
[2025-09-01T17:22:29.230+08:00] Image resize completed
[2025-09-01T17:22:29.230+08:00] Converting to base64
[2025-09-01T17:22:29.230+08:00] screenshotBase64 end
[2025-09-01T17:22:29.230+08:00] adb shell wm,size
[2025-09-01T17:22:29.322+08:00] adb shell wm,size end
[2025-09-01T17:22:29.323+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:29.323+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:29.403+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:29.403+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:29.403+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:29.528+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:29.528+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:29.528+08:00] adb getScreenDensity 
[2025-09-01T17:22:29.625+08:00] adb getScreenDensity  end
[2025-09-01T17:22:41.344+08:00] screenshotBase64 begin
[2025-09-01T17:22:41.344+08:00] adb shell wm,size
[2025-09-01T17:22:41.439+08:00] adb shell wm,size end
[2025-09-01T17:22:41.440+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:41.440+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:41.539+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:41.539+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:41.539+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:41.658+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:41.659+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:41.659+08:00] adb getScreenDensity 
[2025-09-01T17:22:41.746+08:00] adb getScreenDensity  end
[2025-09-01T17:22:41.746+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:41.746+08:00] adb takeScreenshot 
[2025-09-01T17:22:41.939+08:00] adb takeScreenshot  end
[2025-09-01T17:22:41.939+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:41.939+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:41.939+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:41.939+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:41.940+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png
[2025-09-01T17:22:44.317+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png end
[2025-09-01T17:22:44.317+08:00] adb.shell screencap completed
[2025-09-01T17:22:44.317+08:00] Pulling screenshot file from device
[2025-09-01T17:22:44.318+08:00] adb pull /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k7mnw8ql0y.png
[2025-09-01T17:22:44.455+08:00] adb pull /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k7mnw8ql0y.png end
[2025-09-01T17:22:44.455+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\k7mnw8ql0y.png
[2025-09-01T17:22:44.459+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png
[2025-09-01T17:22:44.526+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a9b926f5-d6b8-4270-ad17-69cb067153a1.png end
[2025-09-01T17:22:44.526+08:00] Resizing screenshot image
[2025-09-01T17:22:44.713+08:00] Image resize completed
[2025-09-01T17:22:44.713+08:00] Converting to base64
[2025-09-01T17:22:44.713+08:00] screenshotBase64 end
[2025-09-01T17:22:44.928+08:00] screenshotBase64 begin
[2025-09-01T17:22:44.928+08:00] adb shell wm,size
[2025-09-01T17:22:45.033+08:00] adb shell wm,size end
[2025-09-01T17:22:45.033+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:45.034+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:45.127+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:45.127+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:45.127+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:45.246+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:45.246+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:45.246+08:00] adb getScreenDensity 
[2025-09-01T17:22:45.351+08:00] adb getScreenDensity  end
[2025-09-01T17:22:45.351+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:45.351+08:00] adb takeScreenshot 
[2025-09-01T17:22:45.539+08:00] adb takeScreenshot  end
[2025-09-01T17:22:45.539+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:45.539+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:45.539+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:45.540+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:45.540+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png
[2025-09-01T17:22:47.886+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png end
[2025-09-01T17:22:47.886+08:00] adb.shell screencap completed
[2025-09-01T17:22:47.886+08:00] Pulling screenshot file from device
[2025-09-01T17:22:47.886+08:00] adb pull /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ox976afoluk.png
[2025-09-01T17:22:48.015+08:00] adb pull /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ox976afoluk.png end
[2025-09-01T17:22:48.015+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ox976afoluk.png
[2025-09-01T17:22:48.022+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png
[2025-09-01T17:22:48.096+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a0e7f979-50f0-4d2e-add1-dd2691ffa3ad.png end
[2025-09-01T17:22:48.096+08:00] Resizing screenshot image
[2025-09-01T17:22:48.288+08:00] Image resize completed
[2025-09-01T17:22:48.288+08:00] Converting to base64
[2025-09-01T17:22:48.288+08:00] screenshotBase64 end
[2025-09-01T17:22:48.396+08:00] screenshotBase64 begin
[2025-09-01T17:22:48.396+08:00] adb shell wm,size
[2025-09-01T17:22:48.506+08:00] adb shell wm,size end
[2025-09-01T17:22:48.506+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:48.506+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:48.603+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:48.603+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:48.603+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:48.720+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:48.720+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:48.720+08:00] adb getScreenDensity 
[2025-09-01T17:22:48.799+08:00] adb getScreenDensity  end
[2025-09-01T17:22:48.799+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:48.799+08:00] adb takeScreenshot 
[2025-09-01T17:22:48.984+08:00] adb takeScreenshot  end
[2025-09-01T17:22:48.984+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:48.984+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:48.984+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:48.984+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:48.984+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png
[2025-09-01T17:22:51.364+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png end
[2025-09-01T17:22:51.364+08:00] adb.shell screencap completed
[2025-09-01T17:22:51.364+08:00] Pulling screenshot file from device
[2025-09-01T17:22:51.364+08:00] adb pull /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8bgcxbcf8ly.png
[2025-09-01T17:22:51.524+08:00] adb pull /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8bgcxbcf8ly.png end
[2025-09-01T17:22:51.525+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8bgcxbcf8ly.png
[2025-09-01T17:22:51.528+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png
[2025-09-01T17:22:51.596+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ad7258eb-82f5-4e5c-a4c9-8f70d9d6d4f7.png end
[2025-09-01T17:22:51.596+08:00] Resizing screenshot image
[2025-09-01T17:22:51.793+08:00] Image resize completed
[2025-09-01T17:22:51.793+08:00] Converting to base64
[2025-09-01T17:22:51.793+08:00] screenshotBase64 end
[2025-09-01T17:22:51.857+08:00] screenshotBase64 begin
[2025-09-01T17:22:51.857+08:00] adb shell wm,size
[2025-09-01T17:22:51.942+08:00] adb shell wm,size end
[2025-09-01T17:22:51.942+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:51.942+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:52.037+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:52.037+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:52.037+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:52.145+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:52.145+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:52.145+08:00] adb getScreenDensity 
[2025-09-01T17:22:52.223+08:00] adb getScreenDensity  end
[2025-09-01T17:22:52.223+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:52.223+08:00] adb takeScreenshot 
[2025-09-01T17:22:52.415+08:00] adb takeScreenshot  end
[2025-09-01T17:22:52.415+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:52.415+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:52.415+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:52.415+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:52.415+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png
[2025-09-01T17:22:54.781+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png end
[2025-09-01T17:22:54.781+08:00] adb.shell screencap completed
[2025-09-01T17:22:54.781+08:00] Pulling screenshot file from device
[2025-09-01T17:22:54.781+08:00] adb pull /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5wh1wnaqr8h.png
[2025-09-01T17:22:54.913+08:00] adb pull /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5wh1wnaqr8h.png end
[2025-09-01T17:22:54.913+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5wh1wnaqr8h.png
[2025-09-01T17:22:54.917+08:00] adb shell rm /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png
[2025-09-01T17:22:55.007+08:00] adb shell rm /data/local/tmp/midscene_screenshot_13fa454d-dba1-4503-9c89-7f7fffced692.png end
[2025-09-01T17:22:55.008+08:00] Resizing screenshot image
[2025-09-01T17:22:55.221+08:00] Image resize completed
[2025-09-01T17:22:55.221+08:00] Converting to base64
[2025-09-01T17:22:55.221+08:00] screenshotBase64 end
[2025-09-01T17:22:55.221+08:00] adb shell wm,size
[2025-09-01T17:22:55.345+08:00] adb shell wm,size end
[2025-09-01T17:22:55.345+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:55.345+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:55.461+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:55.461+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:55.461+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:55.601+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:55.601+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:55.601+08:00] adb getScreenDensity 
[2025-09-01T17:22:55.706+08:00] adb getScreenDensity  end
[2025-09-01T17:22:55.770+08:00] screenshotBase64 begin
[2025-09-01T17:22:55.770+08:00] adb shell wm,size
[2025-09-01T17:22:55.864+08:00] adb shell wm,size end
[2025-09-01T17:22:55.864+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:55.864+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:55.954+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:55.954+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:55.954+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:56.077+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:56.077+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:56.078+08:00] adb getScreenDensity 
[2025-09-01T17:22:56.200+08:00] adb getScreenDensity  end
[2025-09-01T17:22:56.201+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:22:56.201+08:00] adb takeScreenshot 
[2025-09-01T17:22:56.375+08:00] adb takeScreenshot  end
[2025-09-01T17:22:56.375+08:00] adb.takeScreenshot completed
[2025-09-01T17:22:56.375+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:22:56.375+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:22:56.376+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:22:56.376+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png
[2025-09-01T17:22:58.736+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png end
[2025-09-01T17:22:58.736+08:00] adb.shell screencap completed
[2025-09-01T17:22:58.736+08:00] Pulling screenshot file from device
[2025-09-01T17:22:58.736+08:00] adb pull /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b7iwbemlp9d.png
[2025-09-01T17:22:58.863+08:00] adb pull /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b7iwbemlp9d.png end
[2025-09-01T17:22:58.863+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b7iwbemlp9d.png
[2025-09-01T17:22:58.867+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png
[2025-09-01T17:22:58.945+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e88db236-eaaf-42aa-983b-d63441131486.png end
[2025-09-01T17:22:58.945+08:00] Resizing screenshot image
[2025-09-01T17:22:59.192+08:00] Image resize completed
[2025-09-01T17:22:59.192+08:00] Converting to base64
[2025-09-01T17:22:59.192+08:00] screenshotBase64 end
[2025-09-01T17:22:59.192+08:00] adb shell wm,size
[2025-09-01T17:22:59.292+08:00] adb shell wm,size end
[2025-09-01T17:22:59.292+08:00] Using Physical size: 1080x2436
[2025-09-01T17:22:59.292+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:22:59.370+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:22:59.371+08:00] Failed to get orientation from input, try display
[2025-09-01T17:22:59.371+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:22:59.514+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:22:59.514+08:00] Screen orientation (fallback): 0
[2025-09-01T17:22:59.514+08:00] adb getScreenDensity 
[2025-09-01T17:22:59.616+08:00] adb getScreenDensity  end
[2025-09-01T17:23:01.936+08:00] screenshotBase64 begin
[2025-09-01T17:23:01.936+08:00] adb shell wm,size
[2025-09-01T17:23:02.035+08:00] adb shell wm,size end
[2025-09-01T17:23:02.035+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:02.035+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:02.126+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:02.126+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:02.126+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:02.287+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:02.287+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:02.287+08:00] adb getScreenDensity 
[2025-09-01T17:23:02.384+08:00] adb getScreenDensity  end
[2025-09-01T17:23:02.384+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:02.384+08:00] adb takeScreenshot 
[2025-09-01T17:23:02.569+08:00] adb takeScreenshot  end
[2025-09-01T17:23:02.569+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:02.569+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:02.569+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:02.569+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:02.569+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png
[2025-09-01T17:23:04.895+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png end
[2025-09-01T17:23:04.895+08:00] adb.shell screencap completed
[2025-09-01T17:23:04.895+08:00] Pulling screenshot file from device
[2025-09-01T17:23:04.896+08:00] adb pull /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8udrjbxu1os.png
[2025-09-01T17:23:05.030+08:00] adb pull /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8udrjbxu1os.png end
[2025-09-01T17:23:05.030+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\8udrjbxu1os.png
[2025-09-01T17:23:05.033+08:00] adb shell rm /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png
[2025-09-01T17:23:05.098+08:00] adb shell rm /data/local/tmp/midscene_screenshot_c992be57-53e7-482f-813b-757ee778a80d.png end
[2025-09-01T17:23:05.098+08:00] Resizing screenshot image
[2025-09-01T17:23:05.291+08:00] Image resize completed
[2025-09-01T17:23:05.291+08:00] Converting to base64
[2025-09-01T17:23:05.291+08:00] screenshotBase64 end
[2025-09-01T17:23:05.401+08:00] screenshotBase64 begin
[2025-09-01T17:23:05.401+08:00] adb shell wm,size
[2025-09-01T17:23:05.512+08:00] adb shell wm,size end
[2025-09-01T17:23:05.513+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:05.513+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:05.606+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:05.606+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:05.606+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:05.727+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:05.727+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:05.727+08:00] adb getScreenDensity 
[2025-09-01T17:23:05.838+08:00] adb getScreenDensity  end
[2025-09-01T17:23:05.838+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:05.838+08:00] adb takeScreenshot 
[2025-09-01T17:23:06.007+08:00] adb takeScreenshot  end
[2025-09-01T17:23:06.007+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:06.007+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:06.007+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:06.008+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:06.008+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png
[2025-09-01T17:23:08.350+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png end
[2025-09-01T17:23:08.350+08:00] adb.shell screencap completed
[2025-09-01T17:23:08.350+08:00] Pulling screenshot file from device
[2025-09-01T17:23:08.350+08:00] adb pull /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zejk8ssdbz8.png
[2025-09-01T17:23:08.483+08:00] adb pull /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zejk8ssdbz8.png end
[2025-09-01T17:23:08.483+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zejk8ssdbz8.png
[2025-09-01T17:23:08.486+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png
[2025-09-01T17:23:08.545+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0e398e02-dac3-487f-9553-3bed5db4e8b7.png end
[2025-09-01T17:23:08.545+08:00] Resizing screenshot image
[2025-09-01T17:23:08.715+08:00] Image resize completed
[2025-09-01T17:23:08.715+08:00] Converting to base64
[2025-09-01T17:23:08.715+08:00] screenshotBase64 end
[2025-09-01T17:23:08.715+08:00] adb shell wm,size
[2025-09-01T17:23:08.806+08:00] adb shell wm,size end
[2025-09-01T17:23:08.806+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:08.806+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:08.887+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:08.887+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:08.887+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:09.011+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:09.011+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:09.011+08:00] adb getScreenDensity 
[2025-09-01T17:23:09.122+08:00] adb getScreenDensity  end
[2025-09-01T17:23:09.178+08:00] screenshotBase64 begin
[2025-09-01T17:23:09.178+08:00] adb shell wm,size
[2025-09-01T17:23:09.273+08:00] adb shell wm,size end
[2025-09-01T17:23:09.273+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:09.273+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:09.365+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:09.365+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:09.365+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:09.486+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:09.486+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:09.486+08:00] adb getScreenDensity 
[2025-09-01T17:23:09.568+08:00] adb getScreenDensity  end
[2025-09-01T17:23:09.568+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:09.568+08:00] adb takeScreenshot 
[2025-09-01T17:23:09.756+08:00] adb takeScreenshot  end
[2025-09-01T17:23:09.756+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:09.756+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:09.756+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:09.756+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:09.757+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png
[2025-09-01T17:23:12.111+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png end
[2025-09-01T17:23:12.111+08:00] adb.shell screencap completed
[2025-09-01T17:23:12.111+08:00] Pulling screenshot file from device
[2025-09-01T17:23:12.111+08:00] adb pull /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6ogno9uzgf.png
[2025-09-01T17:23:12.241+08:00] adb pull /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6ogno9uzgf.png end
[2025-09-01T17:23:12.241+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6ogno9uzgf.png
[2025-09-01T17:23:12.244+08:00] adb shell rm /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png
[2025-09-01T17:23:12.307+08:00] adb shell rm /data/local/tmp/midscene_screenshot_893de489-f9b2-41dc-b1cb-9f398654bd2f.png end
[2025-09-01T17:23:12.307+08:00] Resizing screenshot image
[2025-09-01T17:23:12.491+08:00] Image resize completed
[2025-09-01T17:23:12.491+08:00] Converting to base64
[2025-09-01T17:23:12.491+08:00] screenshotBase64 end
[2025-09-01T17:23:12.491+08:00] adb shell wm,size
[2025-09-01T17:23:12.578+08:00] adb shell wm,size end
[2025-09-01T17:23:12.578+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:12.578+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:12.674+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:12.674+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:12.674+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:12.808+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:12.808+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:12.808+08:00] adb getScreenDensity 
[2025-09-01T17:23:12.903+08:00] adb getScreenDensity  end
[2025-09-01T17:23:14.856+08:00] screenshotBase64 begin
[2025-09-01T17:23:14.856+08:00] adb shell wm,size
[2025-09-01T17:23:14.979+08:00] adb shell wm,size end
[2025-09-01T17:23:14.979+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:14.979+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:15.079+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:15.079+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:15.079+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:15.220+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:15.221+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:15.221+08:00] adb getScreenDensity 
[2025-09-01T17:23:15.328+08:00] adb getScreenDensity  end
[2025-09-01T17:23:15.328+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:15.328+08:00] adb takeScreenshot 
[2025-09-01T17:23:15.525+08:00] adb takeScreenshot  end
[2025-09-01T17:23:15.525+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:15.525+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:15.525+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:15.526+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:15.526+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png
[2025-09-01T17:23:17.874+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png end
[2025-09-01T17:23:17.874+08:00] adb.shell screencap completed
[2025-09-01T17:23:17.874+08:00] Pulling screenshot file from device
[2025-09-01T17:23:17.874+08:00] adb pull /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\gz5xgpom56.png
[2025-09-01T17:23:18.039+08:00] adb pull /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\gz5xgpom56.png end
[2025-09-01T17:23:18.039+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\gz5xgpom56.png
[2025-09-01T17:23:18.043+08:00] adb shell rm /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png
[2025-09-01T17:23:18.124+08:00] adb shell rm /data/local/tmp/midscene_screenshot_c046ecd2-1530-4bd8-a6d5-756bd0e1e6b9.png end
[2025-09-01T17:23:18.124+08:00] Resizing screenshot image
[2025-09-01T17:23:18.318+08:00] Image resize completed
[2025-09-01T17:23:18.318+08:00] Converting to base64
[2025-09-01T17:23:18.318+08:00] screenshotBase64 end
[2025-09-01T17:23:18.318+08:00] screenshotBase64 begin
[2025-09-01T17:23:18.318+08:00] adb shell wm,size
[2025-09-01T17:23:18.410+08:00] adb shell wm,size end
[2025-09-01T17:23:18.410+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:18.410+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:18.495+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:18.495+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:18.495+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:18.625+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:18.625+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:18.625+08:00] adb getScreenDensity 
[2025-09-01T17:23:18.728+08:00] adb getScreenDensity  end
[2025-09-01T17:23:18.728+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:18.728+08:00] adb takeScreenshot 
[2025-09-01T17:23:18.926+08:00] adb takeScreenshot  end
[2025-09-01T17:23:18.926+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:18.926+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:18.926+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:18.926+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:18.927+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png
[2025-09-01T17:23:21.282+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png end
[2025-09-01T17:23:21.282+08:00] adb.shell screencap completed
[2025-09-01T17:23:21.282+08:00] Pulling screenshot file from device
[2025-09-01T17:23:21.282+08:00] adb pull /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\03fv5qin8656.png
[2025-09-01T17:23:21.438+08:00] adb pull /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\03fv5qin8656.png end
[2025-09-01T17:23:21.438+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\03fv5qin8656.png
[2025-09-01T17:23:21.441+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png
[2025-09-01T17:23:21.514+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ffb00e10-38a9-457f-8eb6-36b64b325d7c.png end
[2025-09-01T17:23:21.514+08:00] Resizing screenshot image
[2025-09-01T17:23:21.696+08:00] Image resize completed
[2025-09-01T17:23:21.696+08:00] Converting to base64
[2025-09-01T17:23:21.696+08:00] screenshotBase64 end
[2025-09-01T17:23:44.363+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-09-01T17:23:44.364+08:00] adb shell wm,size
[2025-09-01T17:23:44.474+08:00] adb shell wm,size end
[2025-09-01T17:23:44.474+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:44.474+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:44.554+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:44.554+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:44.554+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:44.686+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:44.686+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:44.687+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-09-01T17:23:44.687+08:00] Launching app: com.transsion.aivoiceassistant
[2025-09-01T17:23:44.687+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-09-01T17:23:45.010+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-09-01T17:23:45.011+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-09-01T17:23:45.075+08:00] screenshotBase64 begin
[2025-09-01T17:23:45.075+08:00] adb shell wm,size
[2025-09-01T17:23:45.227+08:00] adb shell wm,size end
[2025-09-01T17:23:45.227+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:45.227+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:45.335+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:45.335+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:45.335+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:45.477+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:45.477+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:45.478+08:00] adb getScreenDensity 
[2025-09-01T17:23:45.566+08:00] adb getScreenDensity  end
[2025-09-01T17:23:45.566+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:45.566+08:00] adb takeScreenshot 
[2025-09-01T17:23:45.785+08:00] adb takeScreenshot  end
[2025-09-01T17:23:45.785+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:45.785+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:45.785+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:45.787+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:45.787+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png
[2025-09-01T17:23:46.537+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png end
[2025-09-01T17:23:46.537+08:00] adb.shell screencap completed
[2025-09-01T17:23:46.537+08:00] Pulling screenshot file from device
[2025-09-01T17:23:46.537+08:00] adb pull /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\04vceo603k9b.png
[2025-09-01T17:23:46.597+08:00] adb pull /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\04vceo603k9b.png end
[2025-09-01T17:23:46.597+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\04vceo603k9b.png
[2025-09-01T17:23:46.598+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png
[2025-09-01T17:23:46.681+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0421abf3-1936-417e-94ee-237262b32252.png end
[2025-09-01T17:23:46.681+08:00] Resizing screenshot image
[2025-09-01T17:23:46.893+08:00] Image resize completed
[2025-09-01T17:23:46.893+08:00] Converting to base64
[2025-09-01T17:23:46.893+08:00] screenshotBase64 end
[2025-09-01T17:23:46.893+08:00] adb shell wm,size
[2025-09-01T17:23:46.991+08:00] adb shell wm,size end
[2025-09-01T17:23:46.991+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:46.991+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:47.088+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:47.088+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:47.088+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:47.219+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:47.219+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:47.219+08:00] adb getScreenDensity 
[2025-09-01T17:23:47.319+08:00] adb getScreenDensity  end
[2025-09-01T17:23:54.520+08:00] screenshotBase64 begin
[2025-09-01T17:23:54.520+08:00] adb shell wm,size
[2025-09-01T17:23:54.614+08:00] adb shell wm,size end
[2025-09-01T17:23:54.615+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:54.615+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:54.694+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:54.695+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:54.695+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:54.806+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:54.806+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:54.806+08:00] adb getScreenDensity 
[2025-09-01T17:23:54.882+08:00] adb getScreenDensity  end
[2025-09-01T17:23:54.883+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:54.883+08:00] adb takeScreenshot 
[2025-09-01T17:23:55.066+08:00] adb takeScreenshot  end
[2025-09-01T17:23:55.066+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:55.066+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:55.066+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:55.066+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:55.066+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png
[2025-09-01T17:23:55.650+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png end
[2025-09-01T17:23:55.650+08:00] adb.shell screencap completed
[2025-09-01T17:23:55.650+08:00] Pulling screenshot file from device
[2025-09-01T17:23:55.650+08:00] adb pull /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\s7n0cre48rh.png
[2025-09-01T17:23:55.729+08:00] adb pull /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\s7n0cre48rh.png end
[2025-09-01T17:23:55.729+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\s7n0cre48rh.png
[2025-09-01T17:23:55.730+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png
[2025-09-01T17:23:55.794+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a94a38ce-3cfb-4260-98f0-0c6e37bdbfdc.png end
[2025-09-01T17:23:55.794+08:00] Resizing screenshot image
[2025-09-01T17:23:55.925+08:00] Image resize completed
[2025-09-01T17:23:55.925+08:00] Converting to base64
[2025-09-01T17:23:55.925+08:00] screenshotBase64 end
[2025-09-01T17:23:56.140+08:00] screenshotBase64 begin
[2025-09-01T17:23:56.140+08:00] adb shell wm,size
[2025-09-01T17:23:56.258+08:00] adb shell wm,size end
[2025-09-01T17:23:56.258+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:56.258+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:56.344+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:56.344+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:56.344+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:56.473+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:56.473+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:56.473+08:00] adb getScreenDensity 
[2025-09-01T17:23:56.572+08:00] adb getScreenDensity  end
[2025-09-01T17:23:56.572+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:56.572+08:00] adb takeScreenshot 
[2025-09-01T17:23:56.762+08:00] adb takeScreenshot  end
[2025-09-01T17:23:56.762+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:56.762+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:56.762+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:56.763+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:56.763+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png
[2025-09-01T17:23:57.370+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png end
[2025-09-01T17:23:57.370+08:00] adb.shell screencap completed
[2025-09-01T17:23:57.370+08:00] Pulling screenshot file from device
[2025-09-01T17:23:57.370+08:00] adb pull /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\hn4hx6gkxzl.png
[2025-09-01T17:23:57.438+08:00] adb pull /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\hn4hx6gkxzl.png end
[2025-09-01T17:23:57.438+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\hn4hx6gkxzl.png
[2025-09-01T17:23:57.439+08:00] adb shell rm /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png
[2025-09-01T17:23:57.507+08:00] adb shell rm /data/local/tmp/midscene_screenshot_cf4d6084-a224-46a4-b343-7c3bf9a09e98.png end
[2025-09-01T17:23:57.507+08:00] Resizing screenshot image
[2025-09-01T17:23:57.649+08:00] Image resize completed
[2025-09-01T17:23:57.649+08:00] Converting to base64
[2025-09-01T17:23:57.650+08:00] screenshotBase64 end
[2025-09-01T17:23:57.728+08:00] screenshotBase64 begin
[2025-09-01T17:23:57.729+08:00] adb shell wm,size
[2025-09-01T17:23:57.852+08:00] adb shell wm,size end
[2025-09-01T17:23:57.852+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:57.852+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:57.948+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:57.948+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:57.948+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:58.073+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:58.073+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:58.073+08:00] adb getScreenDensity 
[2025-09-01T17:23:58.150+08:00] adb getScreenDensity  end
[2025-09-01T17:23:58.150+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:58.150+08:00] adb takeScreenshot 
[2025-09-01T17:23:58.346+08:00] adb takeScreenshot  end
[2025-09-01T17:23:58.346+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:58.346+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:58.346+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:58.346+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:58.346+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png
[2025-09-01T17:23:58.954+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png end
[2025-09-01T17:23:58.954+08:00] adb.shell screencap completed
[2025-09-01T17:23:58.954+08:00] Pulling screenshot file from device
[2025-09-01T17:23:58.954+08:00] adb pull /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ng7rmnc6e9.png
[2025-09-01T17:23:59.011+08:00] adb pull /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ng7rmnc6e9.png end
[2025-09-01T17:23:59.011+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ng7rmnc6e9.png
[2025-09-01T17:23:59.013+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png
[2025-09-01T17:23:59.078+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ad44d2f2-e216-4ac5-a73f-49829697ef93.png end
[2025-09-01T17:23:59.078+08:00] Resizing screenshot image
[2025-09-01T17:23:59.216+08:00] Image resize completed
[2025-09-01T17:23:59.216+08:00] Converting to base64
[2025-09-01T17:23:59.216+08:00] screenshotBase64 end
[2025-09-01T17:23:59.290+08:00] screenshotBase64 begin
[2025-09-01T17:23:59.291+08:00] adb shell wm,size
[2025-09-01T17:23:59.391+08:00] adb shell wm,size end
[2025-09-01T17:23:59.391+08:00] Using Physical size: 1080x2436
[2025-09-01T17:23:59.391+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:23:59.486+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:23:59.486+08:00] Failed to get orientation from input, try display
[2025-09-01T17:23:59.486+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:23:59.608+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:23:59.608+08:00] Screen orientation (fallback): 0
[2025-09-01T17:23:59.608+08:00] adb getScreenDensity 
[2025-09-01T17:23:59.692+08:00] adb getScreenDensity  end
[2025-09-01T17:23:59.692+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:23:59.692+08:00] adb takeScreenshot 
[2025-09-01T17:23:59.888+08:00] adb takeScreenshot  end
[2025-09-01T17:23:59.888+08:00] adb.takeScreenshot completed
[2025-09-01T17:23:59.888+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:23:59.888+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:23:59.888+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:23:59.888+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png
[2025-09-01T17:24:00.470+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png end
[2025-09-01T17:24:00.470+08:00] adb.shell screencap completed
[2025-09-01T17:24:00.470+08:00] Pulling screenshot file from device
[2025-09-01T17:24:00.470+08:00] adb pull /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\rhs97rem7ua.png
[2025-09-01T17:24:00.534+08:00] adb pull /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\rhs97rem7ua.png end
[2025-09-01T17:24:00.534+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\rhs97rem7ua.png
[2025-09-01T17:24:00.535+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png
[2025-09-01T17:24:00.597+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d5027294-5e3b-417e-a4bc-465975e4a3dd.png end
[2025-09-01T17:24:00.597+08:00] Resizing screenshot image
[2025-09-01T17:24:00.740+08:00] Image resize completed
[2025-09-01T17:24:00.740+08:00] Converting to base64
[2025-09-01T17:24:00.740+08:00] screenshotBase64 end
[2025-09-01T17:24:00.740+08:00] adb shell wm,size
[2025-09-01T17:24:00.826+08:00] adb shell wm,size end
[2025-09-01T17:24:00.826+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:00.826+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:00.898+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:00.898+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:00.898+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:00.989+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:00.989+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:00.989+08:00] adb getScreenDensity 
[2025-09-01T17:24:01.091+08:00] adb getScreenDensity  end
[2025-09-01T17:24:01.154+08:00] screenshotBase64 begin
[2025-09-01T17:24:01.154+08:00] adb shell wm,size
[2025-09-01T17:24:01.249+08:00] adb shell wm,size end
[2025-09-01T17:24:01.249+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:01.249+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:01.363+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:01.363+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:01.363+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:01.481+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:01.481+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:01.481+08:00] adb getScreenDensity 
[2025-09-01T17:24:01.599+08:00] adb getScreenDensity  end
[2025-09-01T17:24:01.599+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:01.599+08:00] adb takeScreenshot 
[2025-09-01T17:24:01.799+08:00] adb takeScreenshot  end
[2025-09-01T17:24:01.799+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:01.799+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:01.799+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:01.800+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:01.800+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png
[2025-09-01T17:24:02.395+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png end
[2025-09-01T17:24:02.395+08:00] adb.shell screencap completed
[2025-09-01T17:24:02.395+08:00] Pulling screenshot file from device
[2025-09-01T17:24:02.395+08:00] adb pull /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6261ka595h.png
[2025-09-01T17:24:02.454+08:00] adb pull /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6261ka595h.png end
[2025-09-01T17:24:02.454+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6261ka595h.png
[2025-09-01T17:24:02.455+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png
[2025-09-01T17:24:02.516+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b2bfddf9-977e-4aa4-89e9-20282237d3c1.png end
[2025-09-01T17:24:02.516+08:00] Resizing screenshot image
[2025-09-01T17:24:02.648+08:00] Image resize completed
[2025-09-01T17:24:02.648+08:00] Converting to base64
[2025-09-01T17:24:02.648+08:00] screenshotBase64 end
[2025-09-01T17:24:02.648+08:00] adb shell wm,size
[2025-09-01T17:24:02.745+08:00] adb shell wm,size end
[2025-09-01T17:24:02.745+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:02.746+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:02.832+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:02.832+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:02.832+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:02.940+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:02.940+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:02.940+08:00] adb getScreenDensity 
[2025-09-01T17:24:03.051+08:00] adb getScreenDensity  end
[2025-09-01T17:24:05.387+08:00] screenshotBase64 begin
[2025-09-01T17:24:05.387+08:00] adb shell wm,size
[2025-09-01T17:24:05.477+08:00] adb shell wm,size end
[2025-09-01T17:24:05.478+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:05.478+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:05.599+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:05.600+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:05.600+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:05.754+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:05.755+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:05.755+08:00] adb getScreenDensity 
[2025-09-01T17:24:05.855+08:00] adb getScreenDensity  end
[2025-09-01T17:24:05.855+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:05.855+08:00] adb takeScreenshot 
[2025-09-01T17:24:06.046+08:00] adb takeScreenshot  end
[2025-09-01T17:24:06.046+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:06.046+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:06.046+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:06.046+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:06.046+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png
[2025-09-01T17:24:06.629+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png end
[2025-09-01T17:24:06.629+08:00] adb.shell screencap completed
[2025-09-01T17:24:06.629+08:00] Pulling screenshot file from device
[2025-09-01T17:24:06.629+08:00] adb pull /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g1sxuu5uel.png
[2025-09-01T17:24:06.681+08:00] adb pull /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g1sxuu5uel.png end
[2025-09-01T17:24:06.681+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g1sxuu5uel.png
[2025-09-01T17:24:06.682+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png
[2025-09-01T17:24:06.763+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0d2f9e83-c956-43b9-9fc1-a45cfe6c5f51.png end
[2025-09-01T17:24:06.763+08:00] Resizing screenshot image
[2025-09-01T17:24:06.916+08:00] Image resize completed
[2025-09-01T17:24:06.916+08:00] Converting to base64
[2025-09-01T17:24:06.916+08:00] screenshotBase64 end
[2025-09-01T17:24:06.916+08:00] adb shell wm,size
[2025-09-01T17:24:07.016+08:00] adb shell wm,size end
[2025-09-01T17:24:07.017+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:07.017+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:07.131+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:07.131+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:07.131+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:07.273+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:07.273+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:07.273+08:00] adb getScreenDensity 
[2025-09-01T17:24:07.383+08:00] adb getScreenDensity  end
[2025-09-01T17:24:12.039+08:00] screenshotBase64 begin
[2025-09-01T17:24:12.039+08:00] adb shell wm,size
[2025-09-01T17:24:12.141+08:00] adb shell wm,size end
[2025-09-01T17:24:12.141+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:12.141+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:12.233+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:12.233+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:12.233+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:12.357+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:12.357+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:12.357+08:00] adb getScreenDensity 
[2025-09-01T17:24:12.464+08:00] adb getScreenDensity  end
[2025-09-01T17:24:12.464+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:12.464+08:00] adb takeScreenshot 
[2025-09-01T17:24:12.645+08:00] adb takeScreenshot  end
[2025-09-01T17:24:12.645+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:12.645+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:12.645+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:12.646+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:12.646+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png
[2025-09-01T17:24:13.222+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png end
[2025-09-01T17:24:13.222+08:00] adb.shell screencap completed
[2025-09-01T17:24:13.222+08:00] Pulling screenshot file from device
[2025-09-01T17:24:13.222+08:00] adb pull /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a46c6biuuf9.png
[2025-09-01T17:24:13.280+08:00] adb pull /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a46c6biuuf9.png end
[2025-09-01T17:24:13.280+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\a46c6biuuf9.png
[2025-09-01T17:24:13.282+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png
[2025-09-01T17:24:13.348+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4d86f8c1-b93f-4b66-9eb0-f4f5b5884db3.png end
[2025-09-01T17:24:13.348+08:00] Resizing screenshot image
[2025-09-01T17:24:13.482+08:00] Image resize completed
[2025-09-01T17:24:13.482+08:00] Converting to base64
[2025-09-01T17:24:13.482+08:00] screenshotBase64 end
[2025-09-01T17:24:13.482+08:00] adb shell wm,size
[2025-09-01T17:24:13.571+08:00] adb shell wm,size end
[2025-09-01T17:24:13.571+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:13.571+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:13.661+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:13.661+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:13.661+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:13.808+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:13.808+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:13.808+08:00] adb getScreenDensity 
[2025-09-01T17:24:13.910+08:00] adb getScreenDensity  end
[2025-09-01T17:24:13.913+08:00] screenshotBase64 begin
[2025-09-01T17:24:13.913+08:00] adb shell wm,size
[2025-09-01T17:24:14.016+08:00] adb shell wm,size end
[2025-09-01T17:24:14.017+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:14.017+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:14.131+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:14.131+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:14.131+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:14.266+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:14.266+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:14.266+08:00] adb getScreenDensity 
[2025-09-01T17:24:14.348+08:00] adb getScreenDensity  end
[2025-09-01T17:24:14.348+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:14.348+08:00] adb takeScreenshot 
[2025-09-01T17:24:14.537+08:00] adb takeScreenshot  end
[2025-09-01T17:24:14.537+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:14.537+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:14.538+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:14.538+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:14.538+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png
[2025-09-01T17:24:15.127+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png end
[2025-09-01T17:24:15.127+08:00] adb.shell screencap completed
[2025-09-01T17:24:15.127+08:00] Pulling screenshot file from device
[2025-09-01T17:24:15.127+08:00] adb pull /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\65wlzmixhjl.png
[2025-09-01T17:24:15.180+08:00] adb pull /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\65wlzmixhjl.png end
[2025-09-01T17:24:15.180+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\65wlzmixhjl.png
[2025-09-01T17:24:15.181+08:00] adb shell rm /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png
[2025-09-01T17:24:15.251+08:00] adb shell rm /data/local/tmp/midscene_screenshot_db5d2b71-8322-46ca-b616-820aaa990616.png end
[2025-09-01T17:24:15.251+08:00] Resizing screenshot image
[2025-09-01T17:24:15.400+08:00] Image resize completed
[2025-09-01T17:24:15.400+08:00] Converting to base64
[2025-09-01T17:24:15.401+08:00] screenshotBase64 end
[2025-09-01T17:24:15.460+08:00] screenshotBase64 begin
[2025-09-01T17:24:15.460+08:00] adb shell wm,size
[2025-09-01T17:24:15.554+08:00] adb shell wm,size end
[2025-09-01T17:24:15.554+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:15.554+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:15.644+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:15.645+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:15.645+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:15.743+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:15.743+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:15.743+08:00] adb getScreenDensity 
[2025-09-01T17:24:15.831+08:00] adb getScreenDensity  end
[2025-09-01T17:24:15.831+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:15.831+08:00] adb takeScreenshot 
[2025-09-01T17:24:16.016+08:00] adb takeScreenshot  end
[2025-09-01T17:24:16.016+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:16.016+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:16.016+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:16.017+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:16.018+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png
[2025-09-01T17:24:16.594+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png end
[2025-09-01T17:24:16.594+08:00] adb.shell screencap completed
[2025-09-01T17:24:16.594+08:00] Pulling screenshot file from device
[2025-09-01T17:24:16.594+08:00] adb pull /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\91jm3cbtheu.png
[2025-09-01T17:24:16.647+08:00] adb pull /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\91jm3cbtheu.png end
[2025-09-01T17:24:16.647+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\91jm3cbtheu.png
[2025-09-01T17:24:16.648+08:00] adb shell rm /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png
[2025-09-01T17:24:16.706+08:00] adb shell rm /data/local/tmp/midscene_screenshot_94e686cf-f82a-4365-a631-ff270849db3f.png end
[2025-09-01T17:24:16.706+08:00] Resizing screenshot image
[2025-09-01T17:24:16.845+08:00] Image resize completed
[2025-09-01T17:24:16.845+08:00] Converting to base64
[2025-09-01T17:24:16.845+08:00] screenshotBase64 end
[2025-09-01T17:24:16.845+08:00] adb shell wm,size
[2025-09-01T17:24:16.954+08:00] adb shell wm,size end
[2025-09-01T17:24:16.954+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:16.954+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:17.064+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:17.064+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:17.064+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:17.194+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:17.194+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:17.194+08:00] adb getScreenDensity 
[2025-09-01T17:24:17.285+08:00] adb getScreenDensity  end
[2025-09-01T17:24:17.286+08:00] adb shell input swipe 1268 2481 1268 2481 150
[2025-09-01T17:24:17.544+08:00] adb shell input swipe 1268 2481 1268 2481 150 end
[2025-09-01T17:24:17.758+08:00] screenshotBase64 begin
[2025-09-01T17:24:17.758+08:00] adb shell wm,size
[2025-09-01T17:24:17.857+08:00] adb shell wm,size end
[2025-09-01T17:24:17.857+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:17.857+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:17.956+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:17.956+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:17.956+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:18.096+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:18.096+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:18.096+08:00] adb getScreenDensity 
[2025-09-01T17:24:18.188+08:00] adb getScreenDensity  end
[2025-09-01T17:24:18.188+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:18.188+08:00] adb takeScreenshot 
[2025-09-01T17:24:18.387+08:00] adb takeScreenshot  end
[2025-09-01T17:24:18.387+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:18.387+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:18.387+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:18.387+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:18.387+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png
[2025-09-01T17:24:19.029+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png end
[2025-09-01T17:24:19.029+08:00] adb.shell screencap completed
[2025-09-01T17:24:19.029+08:00] Pulling screenshot file from device
[2025-09-01T17:24:19.029+08:00] adb pull /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g3lg154n62i.png
[2025-09-01T17:24:19.092+08:00] adb pull /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g3lg154n62i.png end
[2025-09-01T17:24:19.093+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g3lg154n62i.png
[2025-09-01T17:24:19.094+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png
[2025-09-01T17:24:19.166+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e4bb7aac-d7f2-4390-b1b4-0824b54d1d95.png end
[2025-09-01T17:24:19.166+08:00] Resizing screenshot image
[2025-09-01T17:24:19.291+08:00] Image resize completed
[2025-09-01T17:24:19.291+08:00] Converting to base64
[2025-09-01T17:24:19.291+08:00] screenshotBase64 end
[2025-09-01T17:24:19.349+08:00] screenshotBase64 begin
[2025-09-01T17:24:19.349+08:00] adb shell wm,size
[2025-09-01T17:24:19.429+08:00] adb shell wm,size end
[2025-09-01T17:24:19.429+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:19.429+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:19.513+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:19.513+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:19.513+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:19.620+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:19.621+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:19.621+08:00] adb getScreenDensity 
[2025-09-01T17:24:19.715+08:00] adb getScreenDensity  end
[2025-09-01T17:24:19.715+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:19.715+08:00] adb takeScreenshot 
[2025-09-01T17:24:19.916+08:00] adb takeScreenshot  end
[2025-09-01T17:24:19.916+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:19.916+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:19.916+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:19.917+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:19.917+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png
[2025-09-01T17:24:20.486+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png end
[2025-09-01T17:24:20.486+08:00] adb.shell screencap completed
[2025-09-01T17:24:20.486+08:00] Pulling screenshot file from device
[2025-09-01T17:24:20.486+08:00] adb pull /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lydouv6dpcr.png
[2025-09-01T17:24:20.546+08:00] adb pull /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lydouv6dpcr.png end
[2025-09-01T17:24:20.546+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lydouv6dpcr.png
[2025-09-01T17:24:20.547+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png
[2025-09-01T17:24:20.605+08:00] adb shell rm /data/local/tmp/midscene_screenshot_3dc832dc-4cce-4a64-8477-cbd3f1643c6a.png end
[2025-09-01T17:24:20.606+08:00] Resizing screenshot image
[2025-09-01T17:24:20.739+08:00] Image resize completed
[2025-09-01T17:24:20.740+08:00] Converting to base64
[2025-09-01T17:24:20.740+08:00] screenshotBase64 end
[2025-09-01T17:24:20.740+08:00] adb shell wm,size
[2025-09-01T17:24:20.839+08:00] adb shell wm,size end
[2025-09-01T17:24:20.839+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:20.839+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:20.918+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:20.918+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:20.918+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:21.042+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:21.042+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:21.042+08:00] adb getScreenDensity 
[2025-09-01T17:24:21.161+08:00] adb getScreenDensity  end
[2025-09-01T17:24:23.218+08:00] screenshotBase64 begin
[2025-09-01T17:24:23.218+08:00] adb shell wm,size
[2025-09-01T17:24:23.309+08:00] adb shell wm,size end
[2025-09-01T17:24:23.309+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:23.309+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:23.397+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:23.398+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:23.398+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:23.526+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:23.526+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:23.526+08:00] adb getScreenDensity 
[2025-09-01T17:24:23.621+08:00] adb getScreenDensity  end
[2025-09-01T17:24:23.621+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:23.621+08:00] adb takeScreenshot 
[2025-09-01T17:24:23.826+08:00] adb takeScreenshot  end
[2025-09-01T17:24:23.826+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:23.826+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:23.826+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:23.827+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:23.827+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png
[2025-09-01T17:24:24.419+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png end
[2025-09-01T17:24:24.419+08:00] adb.shell screencap completed
[2025-09-01T17:24:24.419+08:00] Pulling screenshot file from device
[2025-09-01T17:24:24.419+08:00] adb pull /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v2o68ehgj5f.png
[2025-09-01T17:24:24.475+08:00] adb pull /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v2o68ehgj5f.png end
[2025-09-01T17:24:24.475+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\v2o68ehgj5f.png
[2025-09-01T17:24:24.476+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png
[2025-09-01T17:24:24.538+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5cdc97b3-513f-49a7-a639-ade789002ac6.png end
[2025-09-01T17:24:24.538+08:00] Resizing screenshot image
[2025-09-01T17:24:24.664+08:00] Image resize completed
[2025-09-01T17:24:24.664+08:00] Converting to base64
[2025-09-01T17:24:24.664+08:00] screenshotBase64 end
[2025-09-01T17:24:24.717+08:00] screenshotBase64 begin
[2025-09-01T17:24:24.717+08:00] adb shell wm,size
[2025-09-01T17:24:24.808+08:00] adb shell wm,size end
[2025-09-01T17:24:24.808+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:24.808+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:24.901+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:24.902+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:24.902+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:25.019+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:25.019+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:25.019+08:00] adb getScreenDensity 
[2025-09-01T17:24:25.113+08:00] adb getScreenDensity  end
[2025-09-01T17:24:25.113+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:25.113+08:00] adb takeScreenshot 
[2025-09-01T17:24:25.302+08:00] adb takeScreenshot  end
[2025-09-01T17:24:25.302+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:25.302+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:25.302+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:25.303+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:25.303+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png
[2025-09-01T17:24:25.913+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png end
[2025-09-01T17:24:25.913+08:00] adb.shell screencap completed
[2025-09-01T17:24:25.913+08:00] Pulling screenshot file from device
[2025-09-01T17:24:25.913+08:00] adb pull /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\pshn6z2t7d.png
[2025-09-01T17:24:25.970+08:00] adb pull /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\pshn6z2t7d.png end
[2025-09-01T17:24:25.970+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\pshn6z2t7d.png
[2025-09-01T17:24:25.971+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png
[2025-09-01T17:24:26.041+08:00] adb shell rm /data/local/tmp/midscene_screenshot_e32b1a36-08ba-45d1-89f3-f9dcf06b9e27.png end
[2025-09-01T17:24:26.041+08:00] Resizing screenshot image
[2025-09-01T17:24:26.177+08:00] Image resize completed
[2025-09-01T17:24:26.177+08:00] Converting to base64
[2025-09-01T17:24:26.177+08:00] screenshotBase64 end
[2025-09-01T17:24:26.177+08:00] adb shell wm,size
[2025-09-01T17:24:26.256+08:00] adb shell wm,size end
[2025-09-01T17:24:26.256+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:26.256+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:26.348+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:26.348+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:26.348+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:26.464+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:26.464+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:26.464+08:00] adb getScreenDensity 
[2025-09-01T17:24:26.564+08:00] adb getScreenDensity  end
[2025-09-01T17:24:26.565+08:00] adb inputText what\'s the weather today
[2025-09-01T17:24:27.835+08:00] adb inputText what\'s the weather today end
[2025-09-01T17:24:27.835+08:00] adb isSoftKeyboardPresent 
[2025-09-01T17:24:28.163+08:00] adb isSoftKeyboardPresent  end
[2025-09-01T17:24:28.163+08:00] Keyboard has no UI; no closing necessary
[2025-09-01T17:24:28.367+08:00] screenshotBase64 begin
[2025-09-01T17:24:28.367+08:00] adb shell wm,size
[2025-09-01T17:24:28.488+08:00] adb shell wm,size end
[2025-09-01T17:24:28.488+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:28.488+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:28.591+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:28.591+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:28.591+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:28.748+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:28.748+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:28.748+08:00] adb getScreenDensity 
[2025-09-01T17:24:28.836+08:00] adb getScreenDensity  end
[2025-09-01T17:24:28.836+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:28.836+08:00] adb takeScreenshot 
[2025-09-01T17:24:29.014+08:00] adb takeScreenshot  end
[2025-09-01T17:24:29.015+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:29.015+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:29.015+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:29.015+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:29.015+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png
[2025-09-01T17:24:29.532+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png end
[2025-09-01T17:24:29.532+08:00] adb.shell screencap completed
[2025-09-01T17:24:29.532+08:00] Pulling screenshot file from device
[2025-09-01T17:24:29.532+08:00] adb pull /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\nvt35o6bwv8.png
[2025-09-01T17:24:29.584+08:00] adb pull /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\nvt35o6bwv8.png end
[2025-09-01T17:24:29.584+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\nvt35o6bwv8.png
[2025-09-01T17:24:29.585+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png
[2025-09-01T17:24:29.649+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d1d44343-782c-401a-b21a-5f7dbf610548.png end
[2025-09-01T17:24:29.649+08:00] Resizing screenshot image
[2025-09-01T17:24:29.776+08:00] Image resize completed
[2025-09-01T17:24:29.776+08:00] Converting to base64
[2025-09-01T17:24:29.777+08:00] screenshotBase64 end
[2025-09-01T17:24:29.860+08:00] screenshotBase64 begin
[2025-09-01T17:24:29.860+08:00] adb shell wm,size
[2025-09-01T17:24:29.958+08:00] adb shell wm,size end
[2025-09-01T17:24:29.958+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:29.958+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:30.065+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:30.065+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:30.065+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:30.205+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:30.205+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:30.205+08:00] adb getScreenDensity 
[2025-09-01T17:24:30.281+08:00] adb getScreenDensity  end
[2025-09-01T17:24:30.281+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:30.281+08:00] adb takeScreenshot 
[2025-09-01T17:24:30.488+08:00] adb takeScreenshot  end
[2025-09-01T17:24:30.488+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:30.488+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:30.488+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:30.488+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:30.488+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png
[2025-09-01T17:24:31.018+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png end
[2025-09-01T17:24:31.018+08:00] adb.shell screencap completed
[2025-09-01T17:24:31.018+08:00] Pulling screenshot file from device
[2025-09-01T17:24:31.018+08:00] adb pull /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zvhervv2asq.png
[2025-09-01T17:24:31.071+08:00] adb pull /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zvhervv2asq.png end
[2025-09-01T17:24:31.071+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zvhervv2asq.png
[2025-09-01T17:24:31.072+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png
[2025-09-01T17:24:31.128+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ab8535af-3c1e-4554-b82b-e877abba3259.png end
[2025-09-01T17:24:31.128+08:00] Resizing screenshot image
[2025-09-01T17:24:31.260+08:00] Image resize completed
[2025-09-01T17:24:31.260+08:00] Converting to base64
[2025-09-01T17:24:31.260+08:00] screenshotBase64 end
[2025-09-01T17:24:31.260+08:00] adb shell wm,size
[2025-09-01T17:24:31.356+08:00] adb shell wm,size end
[2025-09-01T17:24:31.356+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:31.356+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:31.457+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:31.457+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:31.457+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:31.573+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:31.573+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:31.573+08:00] adb getScreenDensity 
[2025-09-01T17:24:31.673+08:00] adb getScreenDensity  end
[2025-09-01T17:24:34.057+08:00] screenshotBase64 begin
[2025-09-01T17:24:34.057+08:00] adb shell wm,size
[2025-09-01T17:24:34.134+08:00] adb shell wm,size end
[2025-09-01T17:24:34.134+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:34.134+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:34.207+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:34.207+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:34.207+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:34.309+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:34.309+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:34.309+08:00] adb getScreenDensity 
[2025-09-01T17:24:34.395+08:00] adb getScreenDensity  end
[2025-09-01T17:24:34.395+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:34.395+08:00] adb takeScreenshot 
[2025-09-01T17:24:34.574+08:00] adb takeScreenshot  end
[2025-09-01T17:24:34.574+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:34.574+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:34.575+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:34.575+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:34.575+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png
[2025-09-01T17:24:35.113+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png end
[2025-09-01T17:24:35.113+08:00] adb.shell screencap completed
[2025-09-01T17:24:35.113+08:00] Pulling screenshot file from device
[2025-09-01T17:24:35.113+08:00] adb pull /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b5ozl5nol3h.png
[2025-09-01T17:24:35.165+08:00] adb pull /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b5ozl5nol3h.png end
[2025-09-01T17:24:35.165+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\b5ozl5nol3h.png
[2025-09-01T17:24:35.166+08:00] adb shell rm /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png
[2025-09-01T17:24:35.233+08:00] adb shell rm /data/local/tmp/midscene_screenshot_129e3bf1-8045-4149-8fa3-8f9ccb0403f0.png end
[2025-09-01T17:24:35.233+08:00] Resizing screenshot image
[2025-09-01T17:24:35.394+08:00] Image resize completed
[2025-09-01T17:24:35.394+08:00] Converting to base64
[2025-09-01T17:24:35.394+08:00] screenshotBase64 end
[2025-09-01T17:24:35.394+08:00] adb shell wm,size
[2025-09-01T17:24:35.494+08:00] adb shell wm,size end
[2025-09-01T17:24:35.494+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:35.494+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:35.586+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:35.586+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:35.586+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:35.710+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:35.710+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:35.710+08:00] adb getScreenDensity 
[2025-09-01T17:24:35.807+08:00] adb getScreenDensity  end
[2025-09-01T17:24:35.808+08:00] screenshotBase64 begin
[2025-09-01T17:24:35.808+08:00] adb shell wm,size
[2025-09-01T17:24:35.895+08:00] adb shell wm,size end
[2025-09-01T17:24:35.895+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:35.895+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:35.988+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:35.988+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:35.988+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:36.108+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:36.109+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:36.109+08:00] adb getScreenDensity 
[2025-09-01T17:24:36.210+08:00] adb getScreenDensity  end
[2025-09-01T17:24:36.210+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:36.210+08:00] adb takeScreenshot 
[2025-09-01T17:24:36.406+08:00] adb takeScreenshot  end
[2025-09-01T17:24:36.406+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:36.406+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:36.406+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:36.406+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:36.406+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png
[2025-09-01T17:24:36.967+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png end
[2025-09-01T17:24:36.967+08:00] adb.shell screencap completed
[2025-09-01T17:24:36.967+08:00] Pulling screenshot file from device
[2025-09-01T17:24:36.967+08:00] adb pull /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\of32lv5hge.png
[2025-09-01T17:24:37.035+08:00] adb pull /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\of32lv5hge.png end
[2025-09-01T17:24:37.035+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\of32lv5hge.png
[2025-09-01T17:24:37.035+08:00] adb shell rm /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png
[2025-09-01T17:24:37.109+08:00] adb shell rm /data/local/tmp/midscene_screenshot_892c58b8-0203-44a7-980b-2976603e3acd.png end
[2025-09-01T17:24:37.109+08:00] Resizing screenshot image
[2025-09-01T17:24:37.243+08:00] Image resize completed
[2025-09-01T17:24:37.243+08:00] Converting to base64
[2025-09-01T17:24:37.243+08:00] screenshotBase64 end
[2025-09-01T17:24:37.297+08:00] screenshotBase64 begin
[2025-09-01T17:24:37.297+08:00] adb shell wm,size
[2025-09-01T17:24:37.395+08:00] adb shell wm,size end
[2025-09-01T17:24:37.395+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:37.395+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:37.478+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:37.478+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:37.478+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:37.586+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:37.586+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:37.586+08:00] adb getScreenDensity 
[2025-09-01T17:24:37.679+08:00] adb getScreenDensity  end
[2025-09-01T17:24:37.679+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:37.679+08:00] adb takeScreenshot 
[2025-09-01T17:24:37.874+08:00] adb takeScreenshot  end
[2025-09-01T17:24:37.874+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:37.874+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:37.874+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:37.874+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:37.874+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png
[2025-09-01T17:24:38.402+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png end
[2025-09-01T17:24:38.402+08:00] adb.shell screencap completed
[2025-09-01T17:24:38.402+08:00] Pulling screenshot file from device
[2025-09-01T17:24:38.402+08:00] adb pull /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ivqj2i1cx0i.png
[2025-09-01T17:24:38.457+08:00] adb pull /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ivqj2i1cx0i.png end
[2025-09-01T17:24:38.457+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ivqj2i1cx0i.png
[2025-09-01T17:24:38.458+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png
[2025-09-01T17:24:38.534+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f4a41289-8ddf-4c28-ace4-22c1eac7a324.png end
[2025-09-01T17:24:38.534+08:00] Resizing screenshot image
[2025-09-01T17:24:38.668+08:00] Image resize completed
[2025-09-01T17:24:38.668+08:00] Converting to base64
[2025-09-01T17:24:38.668+08:00] screenshotBase64 end
[2025-09-01T17:24:38.668+08:00] adb shell wm,size
[2025-09-01T17:24:38.775+08:00] adb shell wm,size end
[2025-09-01T17:24:38.775+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:38.775+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:38.870+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:38.870+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:38.870+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:39.012+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:39.012+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:39.012+08:00] adb getScreenDensity 
[2025-09-01T17:24:39.173+08:00] adb getScreenDensity  end
[2025-09-01T17:24:39.174+08:00] adb shell input swipe 217 193 217 193 150
[2025-09-01T17:24:39.432+08:00] adb shell input swipe 217 193 217 193 150 end
[2025-09-01T17:24:39.646+08:00] screenshotBase64 begin
[2025-09-01T17:24:39.646+08:00] adb shell wm,size
[2025-09-01T17:24:39.737+08:00] adb shell wm,size end
[2025-09-01T17:24:39.737+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:39.737+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:39.861+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:39.861+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:39.861+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:40.035+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:40.035+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:40.035+08:00] adb getScreenDensity 
[2025-09-01T17:24:40.125+08:00] adb getScreenDensity  end
[2025-09-01T17:24:40.125+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:40.125+08:00] adb takeScreenshot 
[2025-09-01T17:24:40.379+08:00] adb takeScreenshot  end
[2025-09-01T17:24:40.379+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:40.379+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:40.379+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:40.379+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:40.379+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png
[2025-09-01T17:24:40.923+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png end
[2025-09-01T17:24:40.923+08:00] adb.shell screencap completed
[2025-09-01T17:24:40.923+08:00] Pulling screenshot file from device
[2025-09-01T17:24:40.923+08:00] adb pull /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\2f9glm9nt19.png
[2025-09-01T17:24:40.983+08:00] adb pull /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\2f9glm9nt19.png end
[2025-09-01T17:24:40.983+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\2f9glm9nt19.png
[2025-09-01T17:24:40.984+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png
[2025-09-01T17:24:41.045+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ab50cdf2-5866-43b6-8467-15c29de5d9f6.png end
[2025-09-01T17:24:41.045+08:00] Resizing screenshot image
[2025-09-01T17:24:41.173+08:00] Image resize completed
[2025-09-01T17:24:41.173+08:00] Converting to base64
[2025-09-01T17:24:41.173+08:00] screenshotBase64 end
[2025-09-01T17:24:41.239+08:00] screenshotBase64 begin
[2025-09-01T17:24:41.239+08:00] adb shell wm,size
[2025-09-01T17:24:41.336+08:00] adb shell wm,size end
[2025-09-01T17:24:41.336+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:41.336+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:41.431+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:41.431+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:41.431+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:41.565+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:41.565+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:41.565+08:00] adb getScreenDensity 
[2025-09-01T17:24:41.717+08:00] adb getScreenDensity  end
[2025-09-01T17:24:41.717+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:41.717+08:00] adb takeScreenshot 
[2025-09-01T17:24:41.934+08:00] adb takeScreenshot  end
[2025-09-01T17:24:41.934+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:41.934+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:41.934+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:41.934+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:41.934+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png
[2025-09-01T17:24:42.513+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png end
[2025-09-01T17:24:42.513+08:00] adb.shell screencap completed
[2025-09-01T17:24:42.513+08:00] Pulling screenshot file from device
[2025-09-01T17:24:42.513+08:00] adb pull /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r98lrtcs91s.png
[2025-09-01T17:24:42.568+08:00] adb pull /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r98lrtcs91s.png end
[2025-09-01T17:24:42.568+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\r98lrtcs91s.png
[2025-09-01T17:24:42.570+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png
[2025-09-01T17:24:42.636+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ef17794e-b8c7-4911-8a5d-875ecb66106b.png end
[2025-09-01T17:24:42.636+08:00] Resizing screenshot image
[2025-09-01T17:24:42.773+08:00] Image resize completed
[2025-09-01T17:24:42.773+08:00] Converting to base64
[2025-09-01T17:24:42.773+08:00] screenshotBase64 end
[2025-09-01T17:24:42.773+08:00] adb shell wm,size
[2025-09-01T17:24:42.920+08:00] adb shell wm,size end
[2025-09-01T17:24:42.920+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:42.920+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:43.011+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:43.011+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:43.011+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:43.114+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:43.114+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:43.114+08:00] adb getScreenDensity 
[2025-09-01T17:24:43.209+08:00] adb getScreenDensity  end
[2025-09-01T17:24:46.317+08:00] screenshotBase64 begin
[2025-09-01T17:24:46.317+08:00] adb shell wm,size
[2025-09-01T17:24:46.409+08:00] adb shell wm,size end
[2025-09-01T17:24:46.409+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:46.409+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:46.514+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:46.514+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:46.514+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:46.648+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:46.648+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:46.648+08:00] adb getScreenDensity 
[2025-09-01T17:24:46.748+08:00] adb getScreenDensity  end
[2025-09-01T17:24:46.748+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:46.748+08:00] adb takeScreenshot 
[2025-09-01T17:24:46.952+08:00] adb takeScreenshot  end
[2025-09-01T17:24:46.952+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:46.952+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:46.952+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:46.952+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:46.952+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png
[2025-09-01T17:24:47.566+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png end
[2025-09-01T17:24:47.566+08:00] adb.shell screencap completed
[2025-09-01T17:24:47.566+08:00] Pulling screenshot file from device
[2025-09-01T17:24:47.566+08:00] adb pull /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5lucfkpzhux.png
[2025-09-01T17:24:47.625+08:00] adb pull /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5lucfkpzhux.png end
[2025-09-01T17:24:47.625+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\5lucfkpzhux.png
[2025-09-01T17:24:47.626+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png
[2025-09-01T17:24:47.691+08:00] adb shell rm /data/local/tmp/midscene_screenshot_5b94877d-4cbe-42da-838f-f8b097886f55.png end
[2025-09-01T17:24:47.691+08:00] Resizing screenshot image
[2025-09-01T17:24:47.817+08:00] Image resize completed
[2025-09-01T17:24:47.817+08:00] Converting to base64
[2025-09-01T17:24:47.817+08:00] screenshotBase64 end
[2025-09-01T17:24:47.817+08:00] adb shell wm,size
[2025-09-01T17:24:47.910+08:00] adb shell wm,size end
[2025-09-01T17:24:47.910+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:47.910+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:47.995+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:47.995+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:47.995+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:48.121+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:48.121+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:48.121+08:00] adb getScreenDensity 
[2025-09-01T17:24:48.239+08:00] adb getScreenDensity  end
[2025-09-01T17:24:48.240+08:00] screenshotBase64 begin
[2025-09-01T17:24:48.240+08:00] adb shell wm,size
[2025-09-01T17:24:48.344+08:00] adb shell wm,size end
[2025-09-01T17:24:48.344+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:48.344+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:48.451+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:48.451+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:48.451+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:48.554+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:48.554+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:48.554+08:00] adb getScreenDensity 
[2025-09-01T17:24:48.648+08:00] adb getScreenDensity  end
[2025-09-01T17:24:48.648+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:48.648+08:00] adb takeScreenshot 
[2025-09-01T17:24:48.872+08:00] adb takeScreenshot  end
[2025-09-01T17:24:48.872+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:48.872+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:48.872+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:48.872+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:48.872+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png
[2025-09-01T17:24:49.447+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png end
[2025-09-01T17:24:49.447+08:00] adb.shell screencap completed
[2025-09-01T17:24:49.447+08:00] Pulling screenshot file from device
[2025-09-01T17:24:49.447+08:00] adb pull /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g4mu8uwbgmf.png
[2025-09-01T17:24:49.498+08:00] adb pull /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g4mu8uwbgmf.png end
[2025-09-01T17:24:49.498+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\g4mu8uwbgmf.png
[2025-09-01T17:24:49.499+08:00] adb shell rm /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png
[2025-09-01T17:24:49.561+08:00] adb shell rm /data/local/tmp/midscene_screenshot_502f0699-81e9-4c3f-97ac-f7462a20bb55.png end
[2025-09-01T17:24:49.561+08:00] Resizing screenshot image
[2025-09-01T17:24:49.696+08:00] Image resize completed
[2025-09-01T17:24:49.696+08:00] Converting to base64
[2025-09-01T17:24:49.696+08:00] screenshotBase64 end
[2025-09-01T17:24:49.754+08:00] screenshotBase64 begin
[2025-09-01T17:24:49.754+08:00] adb shell wm,size
[2025-09-01T17:24:49.889+08:00] adb shell wm,size end
[2025-09-01T17:24:49.890+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:49.890+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:49.987+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:49.987+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:49.987+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:50.103+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:50.103+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:50.103+08:00] adb getScreenDensity 
[2025-09-01T17:24:50.214+08:00] adb getScreenDensity  end
[2025-09-01T17:24:50.214+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:50.214+08:00] adb takeScreenshot 
[2025-09-01T17:24:50.389+08:00] adb takeScreenshot  end
[2025-09-01T17:24:50.389+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:50.389+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:50.389+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:50.389+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:50.390+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png
[2025-09-01T17:24:50.963+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png end
[2025-09-01T17:24:50.963+08:00] adb.shell screencap completed
[2025-09-01T17:24:50.963+08:00] Pulling screenshot file from device
[2025-09-01T17:24:50.963+08:00] adb pull /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kvvgzjnuk1d.png
[2025-09-01T17:24:51.026+08:00] adb pull /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kvvgzjnuk1d.png end
[2025-09-01T17:24:51.026+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kvvgzjnuk1d.png
[2025-09-01T17:24:51.027+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png
[2025-09-01T17:24:51.090+08:00] adb shell rm /data/local/tmp/midscene_screenshot_b21fc7dc-ff59-4b45-9c7d-645005d9480e.png end
[2025-09-01T17:24:51.090+08:00] Resizing screenshot image
[2025-09-01T17:24:51.210+08:00] Image resize completed
[2025-09-01T17:24:51.210+08:00] Converting to base64
[2025-09-01T17:24:51.210+08:00] screenshotBase64 end
[2025-09-01T17:24:51.210+08:00] adb shell wm,size
[2025-09-01T17:24:51.301+08:00] adb shell wm,size end
[2025-09-01T17:24:51.301+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:51.301+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:51.384+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:51.384+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:51.384+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:51.496+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:51.496+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:51.496+08:00] adb getScreenDensity 
[2025-09-01T17:24:51.600+08:00] adb getScreenDensity  end
[2025-09-01T17:24:51.600+08:00] adb shell input swipe 217 193 217 193 150
[2025-09-01T17:24:51.859+08:00] adb shell input swipe 217 193 217 193 150 end
[2025-09-01T17:24:52.061+08:00] screenshotBase64 begin
[2025-09-01T17:24:52.061+08:00] adb shell wm,size
[2025-09-01T17:24:52.160+08:00] adb shell wm,size end
[2025-09-01T17:24:52.160+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:52.160+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:52.256+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:52.256+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:52.256+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:52.377+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:52.377+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:52.377+08:00] adb getScreenDensity 
[2025-09-01T17:24:52.492+08:00] adb getScreenDensity  end
[2025-09-01T17:24:52.492+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:52.492+08:00] adb takeScreenshot 
[2025-09-01T17:24:52.694+08:00] adb takeScreenshot  end
[2025-09-01T17:24:52.694+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:52.694+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:52.694+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:52.695+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:52.695+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png
[2025-09-01T17:24:53.266+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png end
[2025-09-01T17:24:53.266+08:00] adb.shell screencap completed
[2025-09-01T17:24:53.266+08:00] Pulling screenshot file from device
[2025-09-01T17:24:53.266+08:00] adb pull /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zcmrv6j5aqn.png
[2025-09-01T17:24:53.315+08:00] adb pull /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zcmrv6j5aqn.png end
[2025-09-01T17:24:53.315+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\zcmrv6j5aqn.png
[2025-09-01T17:24:53.316+08:00] adb shell rm /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png
[2025-09-01T17:24:53.381+08:00] adb shell rm /data/local/tmp/midscene_screenshot_84084e26-3525-4263-ab9f-1e13fc3d22d5.png end
[2025-09-01T17:24:53.381+08:00] Resizing screenshot image
[2025-09-01T17:24:53.496+08:00] Image resize completed
[2025-09-01T17:24:53.496+08:00] Converting to base64
[2025-09-01T17:24:53.496+08:00] screenshotBase64 end
[2025-09-01T17:24:53.559+08:00] screenshotBase64 begin
[2025-09-01T17:24:53.559+08:00] adb shell wm,size
[2025-09-01T17:24:53.646+08:00] adb shell wm,size end
[2025-09-01T17:24:53.646+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:53.646+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:53.742+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:53.742+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:53.742+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:53.876+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:53.877+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:53.877+08:00] adb getScreenDensity 
[2025-09-01T17:24:54.005+08:00] adb getScreenDensity  end
[2025-09-01T17:24:54.005+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:54.005+08:00] adb takeScreenshot 
[2025-09-01T17:24:54.313+08:00] adb takeScreenshot  end
[2025-09-01T17:24:54.313+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:54.313+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:54.313+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:54.313+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:54.313+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png
[2025-09-01T17:24:54.986+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png end
[2025-09-01T17:24:54.986+08:00] adb.shell screencap completed
[2025-09-01T17:24:54.986+08:00] Pulling screenshot file from device
[2025-09-01T17:24:54.986+08:00] adb pull /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6p6wend63vt.png
[2025-09-01T17:24:55.036+08:00] adb pull /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6p6wend63vt.png end
[2025-09-01T17:24:55.036+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\6p6wend63vt.png
[2025-09-01T17:24:55.037+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png
[2025-09-01T17:24:55.118+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d7a5f311-8ba5-4ff0-8c5b-dd458d0cb368.png end
[2025-09-01T17:24:55.118+08:00] Resizing screenshot image
[2025-09-01T17:24:55.244+08:00] Image resize completed
[2025-09-01T17:24:55.245+08:00] Converting to base64
[2025-09-01T17:24:55.245+08:00] screenshotBase64 end
[2025-09-01T17:24:55.245+08:00] adb shell wm,size
[2025-09-01T17:24:55.330+08:00] adb shell wm,size end
[2025-09-01T17:24:55.330+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:55.330+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:55.410+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:55.410+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:55.410+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:55.527+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:55.527+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:55.527+08:00] adb getScreenDensity 
[2025-09-01T17:24:55.637+08:00] adb getScreenDensity  end
[2025-09-01T17:24:58.371+08:00] screenshotBase64 begin
[2025-09-01T17:24:58.371+08:00] adb shell wm,size
[2025-09-01T17:24:58.462+08:00] adb shell wm,size end
[2025-09-01T17:24:58.462+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:58.462+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:58.555+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:58.555+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:58.555+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:24:58.640+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:24:58.640+08:00] Screen orientation (fallback): 0
[2025-09-01T17:24:58.640+08:00] adb getScreenDensity 
[2025-09-01T17:24:58.739+08:00] adb getScreenDensity  end
[2025-09-01T17:24:58.739+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:24:58.739+08:00] adb takeScreenshot 
[2025-09-01T17:24:58.948+08:00] adb takeScreenshot  end
[2025-09-01T17:24:58.948+08:00] adb.takeScreenshot completed
[2025-09-01T17:24:58.949+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:24:58.949+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:24:58.949+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:24:58.949+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png
[2025-09-01T17:24:59.530+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png end
[2025-09-01T17:24:59.531+08:00] adb.shell screencap completed
[2025-09-01T17:24:59.531+08:00] Pulling screenshot file from device
[2025-09-01T17:24:59.531+08:00] adb pull /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\o4ti2slvuw.png
[2025-09-01T17:24:59.579+08:00] adb pull /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\o4ti2slvuw.png end
[2025-09-01T17:24:59.579+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\o4ti2slvuw.png
[2025-09-01T17:24:59.580+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png
[2025-09-01T17:24:59.646+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8ed7e29a-41ef-42f4-a954-9887e3c497e7.png end
[2025-09-01T17:24:59.646+08:00] Resizing screenshot image
[2025-09-01T17:24:59.744+08:00] Image resize completed
[2025-09-01T17:24:59.744+08:00] Converting to base64
[2025-09-01T17:24:59.744+08:00] screenshotBase64 end
[2025-09-01T17:24:59.744+08:00] adb shell wm,size
[2025-09-01T17:24:59.830+08:00] adb shell wm,size end
[2025-09-01T17:24:59.830+08:00] Using Physical size: 1080x2436
[2025-09-01T17:24:59.830+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:24:59.948+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:24:59.948+08:00] Failed to get orientation from input, try display
[2025-09-01T17:24:59.948+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:00.082+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:00.082+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:00.082+08:00] adb getScreenDensity 
[2025-09-01T17:25:00.178+08:00] adb getScreenDensity  end
[2025-09-01T17:25:00.179+08:00] screenshotBase64 begin
[2025-09-01T17:25:00.179+08:00] adb shell wm,size
[2025-09-01T17:25:00.280+08:00] adb shell wm,size end
[2025-09-01T17:25:00.280+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:00.280+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:00.369+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:00.369+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:00.369+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:00.496+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:00.496+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:00.496+08:00] adb getScreenDensity 
[2025-09-01T17:25:00.585+08:00] adb getScreenDensity  end
[2025-09-01T17:25:00.585+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:00.585+08:00] adb takeScreenshot 
[2025-09-01T17:25:00.788+08:00] adb takeScreenshot  end
[2025-09-01T17:25:00.788+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:00.788+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:00.788+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:00.788+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:00.788+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png
[2025-09-01T17:25:01.387+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png end
[2025-09-01T17:25:01.387+08:00] adb.shell screencap completed
[2025-09-01T17:25:01.387+08:00] Pulling screenshot file from device
[2025-09-01T17:25:01.387+08:00] adb pull /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y9rliwiieh.png
[2025-09-01T17:25:01.446+08:00] adb pull /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y9rliwiieh.png end
[2025-09-01T17:25:01.446+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y9rliwiieh.png
[2025-09-01T17:25:01.447+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png
[2025-09-01T17:25:01.544+08:00] adb shell rm /data/local/tmp/midscene_screenshot_fb654f6b-887d-4e2b-93a2-c65e0b6252c8.png end
[2025-09-01T17:25:01.545+08:00] Resizing screenshot image
[2025-09-01T17:25:01.653+08:00] Image resize completed
[2025-09-01T17:25:01.653+08:00] Converting to base64
[2025-09-01T17:25:01.653+08:00] screenshotBase64 end
[2025-09-01T17:25:01.705+08:00] screenshotBase64 begin
[2025-09-01T17:25:01.705+08:00] adb shell wm,size
[2025-09-01T17:25:01.813+08:00] adb shell wm,size end
[2025-09-01T17:25:01.813+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:01.813+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:01.918+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:01.918+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:01.918+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:02.049+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:02.049+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:02.049+08:00] adb getScreenDensity 
[2025-09-01T17:25:02.169+08:00] adb getScreenDensity  end
[2025-09-01T17:25:02.169+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:02.169+08:00] adb takeScreenshot 
[2025-09-01T17:25:02.366+08:00] adb takeScreenshot  end
[2025-09-01T17:25:02.366+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:02.366+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:02.366+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:02.366+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:02.366+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png
[2025-09-01T17:25:02.961+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png end
[2025-09-01T17:25:02.961+08:00] adb.shell screencap completed
[2025-09-01T17:25:02.961+08:00] Pulling screenshot file from device
[2025-09-01T17:25:02.961+08:00] adb pull /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\srhw4qfgmo.png
[2025-09-01T17:25:03.019+08:00] adb pull /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\srhw4qfgmo.png end
[2025-09-01T17:25:03.019+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\srhw4qfgmo.png
[2025-09-01T17:25:03.020+08:00] adb shell rm /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png
[2025-09-01T17:25:03.078+08:00] adb shell rm /data/local/tmp/midscene_screenshot_370824d8-97f3-4d31-ba6c-ee76c0fe9c4f.png end
[2025-09-01T17:25:03.078+08:00] Resizing screenshot image
[2025-09-01T17:25:03.200+08:00] Image resize completed
[2025-09-01T17:25:03.200+08:00] Converting to base64
[2025-09-01T17:25:03.200+08:00] screenshotBase64 end
[2025-09-01T17:25:03.200+08:00] adb shell wm,size
[2025-09-01T17:25:03.286+08:00] adb shell wm,size end
[2025-09-01T17:25:03.286+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:03.286+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:03.390+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:03.390+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:03.390+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:03.515+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:03.515+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:03.515+08:00] adb getScreenDensity 
[2025-09-01T17:25:03.617+08:00] adb getScreenDensity  end
[2025-09-01T17:25:03.618+08:00] adb shell input swipe 217 193 217 193 150
[2025-09-01T17:25:03.875+08:00] adb shell input swipe 217 193 217 193 150 end
[2025-09-01T17:25:04.077+08:00] screenshotBase64 begin
[2025-09-01T17:25:04.078+08:00] adb shell wm,size
[2025-09-01T17:25:04.172+08:00] adb shell wm,size end
[2025-09-01T17:25:04.172+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:04.172+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:04.257+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:04.257+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:04.257+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:04.375+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:04.375+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:04.375+08:00] adb getScreenDensity 
[2025-09-01T17:25:04.474+08:00] adb getScreenDensity  end
[2025-09-01T17:25:04.474+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:04.474+08:00] adb takeScreenshot 
[2025-09-01T17:25:04.670+08:00] adb takeScreenshot  end
[2025-09-01T17:25:04.670+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:04.670+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:04.670+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:04.670+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:04.670+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png
[2025-09-01T17:25:05.275+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png end
[2025-09-01T17:25:05.275+08:00] adb.shell screencap completed
[2025-09-01T17:25:05.275+08:00] Pulling screenshot file from device
[2025-09-01T17:25:05.275+08:00] adb pull /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1otuyf5soya.png
[2025-09-01T17:25:05.323+08:00] adb pull /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1otuyf5soya.png end
[2025-09-01T17:25:05.323+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\1otuyf5soya.png
[2025-09-01T17:25:05.325+08:00] adb shell rm /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png
[2025-09-01T17:25:05.380+08:00] adb shell rm /data/local/tmp/midscene_screenshot_7e915139-c3b4-4d61-8392-faa103c84283.png end
[2025-09-01T17:25:05.381+08:00] Resizing screenshot image
[2025-09-01T17:25:05.496+08:00] Image resize completed
[2025-09-01T17:25:05.496+08:00] Converting to base64
[2025-09-01T17:25:05.496+08:00] screenshotBase64 end
[2025-09-01T17:25:05.558+08:00] screenshotBase64 begin
[2025-09-01T17:25:05.558+08:00] adb shell wm,size
[2025-09-01T17:25:05.647+08:00] adb shell wm,size end
[2025-09-01T17:25:05.647+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:05.647+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:05.751+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:05.751+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:05.751+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:05.867+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:05.867+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:05.867+08:00] adb getScreenDensity 
[2025-09-01T17:25:05.959+08:00] adb getScreenDensity  end
[2025-09-01T17:25:05.959+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:05.959+08:00] adb takeScreenshot 
[2025-09-01T17:25:06.156+08:00] adb takeScreenshot  end
[2025-09-01T17:25:06.156+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:06.156+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:06.156+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:06.156+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:06.156+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png
[2025-09-01T17:25:06.756+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png end
[2025-09-01T17:25:06.757+08:00] adb.shell screencap completed
[2025-09-01T17:25:06.757+08:00] Pulling screenshot file from device
[2025-09-01T17:25:06.757+08:00] adb pull /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\w0bnm0uxzf.png
[2025-09-01T17:25:06.843+08:00] adb pull /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\w0bnm0uxzf.png end
[2025-09-01T17:25:06.844+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\w0bnm0uxzf.png
[2025-09-01T17:25:06.844+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png
[2025-09-01T17:25:06.913+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4e537673-f557-42a9-9726-51732724855c.png end
[2025-09-01T17:25:06.913+08:00] Resizing screenshot image
[2025-09-01T17:25:07.026+08:00] Image resize completed
[2025-09-01T17:25:07.026+08:00] Converting to base64
[2025-09-01T17:25:07.026+08:00] screenshotBase64 end
[2025-09-01T17:25:07.026+08:00] adb shell wm,size
[2025-09-01T17:25:07.113+08:00] adb shell wm,size end
[2025-09-01T17:25:07.113+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:07.113+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:07.228+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:07.228+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:07.228+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:07.375+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:07.376+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:07.376+08:00] adb getScreenDensity 
[2025-09-01T17:25:07.497+08:00] adb getScreenDensity  end
[2025-09-01T17:25:10.743+08:00] screenshotBase64 begin
[2025-09-01T17:25:10.743+08:00] adb shell wm,size
[2025-09-01T17:25:10.809+08:00] adb shell wm,size end
[2025-09-01T17:25:10.809+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:10.809+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:10.891+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:10.892+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:10.892+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:11.001+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:11.001+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:11.001+08:00] adb getScreenDensity 
[2025-09-01T17:25:11.116+08:00] adb getScreenDensity  end
[2025-09-01T17:25:11.116+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:11.116+08:00] adb takeScreenshot 
[2025-09-01T17:25:11.311+08:00] adb takeScreenshot  end
[2025-09-01T17:25:11.311+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:11.311+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:11.311+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:11.311+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:11.311+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png
[2025-09-01T17:25:11.937+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png end
[2025-09-01T17:25:11.937+08:00] adb.shell screencap completed
[2025-09-01T17:25:11.937+08:00] Pulling screenshot file from device
[2025-09-01T17:25:11.937+08:00] adb pull /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lkzp8ru7gzh.png
[2025-09-01T17:25:11.992+08:00] adb pull /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lkzp8ru7gzh.png end
[2025-09-01T17:25:11.992+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lkzp8ru7gzh.png
[2025-09-01T17:25:11.993+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png
[2025-09-01T17:25:12.060+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d19d828d-5ba3-4485-a9d3-b5d73b00e414.png end
[2025-09-01T17:25:12.060+08:00] Resizing screenshot image
[2025-09-01T17:25:12.165+08:00] Image resize completed
[2025-09-01T17:25:12.165+08:00] Converting to base64
[2025-09-01T17:25:12.165+08:00] screenshotBase64 end
[2025-09-01T17:25:12.165+08:00] adb shell wm,size
[2025-09-01T17:25:12.247+08:00] adb shell wm,size end
[2025-09-01T17:25:12.247+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:12.247+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:12.326+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:12.326+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:12.326+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:12.435+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:12.436+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:12.436+08:00] adb getScreenDensity 
[2025-09-01T17:25:12.523+08:00] adb getScreenDensity  end
[2025-09-01T17:25:12.524+08:00] screenshotBase64 begin
[2025-09-01T17:25:12.524+08:00] adb shell wm,size
[2025-09-01T17:25:12.619+08:00] adb shell wm,size end
[2025-09-01T17:25:12.619+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:12.619+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:12.705+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:12.705+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:12.705+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:12.802+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:12.802+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:12.802+08:00] adb getScreenDensity 
[2025-09-01T17:25:12.869+08:00] adb getScreenDensity  end
[2025-09-01T17:25:12.869+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:12.869+08:00] adb takeScreenshot 
[2025-09-01T17:25:13.037+08:00] adb takeScreenshot  end
[2025-09-01T17:25:13.037+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:13.037+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:13.037+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:13.038+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:13.038+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png
[2025-09-01T17:25:13.606+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png end
[2025-09-01T17:25:13.606+08:00] adb.shell screencap completed
[2025-09-01T17:25:13.606+08:00] Pulling screenshot file from device
[2025-09-01T17:25:13.606+08:00] adb pull /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4ql581zbmz9.png
[2025-09-01T17:25:13.652+08:00] adb pull /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4ql581zbmz9.png end
[2025-09-01T17:25:13.652+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4ql581zbmz9.png
[2025-09-01T17:25:13.652+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png
[2025-09-01T17:25:13.709+08:00] adb shell rm /data/local/tmp/midscene_screenshot_8fe6aa97-8581-4e2a-ad09-1fb09e81ed1c.png end
[2025-09-01T17:25:13.709+08:00] Resizing screenshot image
[2025-09-01T17:25:13.817+08:00] Image resize completed
[2025-09-01T17:25:13.817+08:00] Converting to base64
[2025-09-01T17:25:13.817+08:00] screenshotBase64 end
[2025-09-01T17:25:13.888+08:00] screenshotBase64 begin
[2025-09-01T17:25:13.888+08:00] adb shell wm,size
[2025-09-01T17:25:13.985+08:00] adb shell wm,size end
[2025-09-01T17:25:13.986+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:13.986+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:14.085+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:14.085+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:14.085+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:14.218+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:14.218+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:14.218+08:00] adb getScreenDensity 
[2025-09-01T17:25:14.321+08:00] adb getScreenDensity  end
[2025-09-01T17:25:14.321+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:14.321+08:00] adb takeScreenshot 
[2025-09-01T17:25:14.513+08:00] adb takeScreenshot  end
[2025-09-01T17:25:14.513+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:14.513+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:14.513+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:14.514+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:14.514+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png
[2025-09-01T17:25:15.137+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png end
[2025-09-01T17:25:15.137+08:00] adb.shell screencap completed
[2025-09-01T17:25:15.137+08:00] Pulling screenshot file from device
[2025-09-01T17:25:15.137+08:00] adb pull /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwxun6hm89.png
[2025-09-01T17:25:15.189+08:00] adb pull /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwxun6hm89.png end
[2025-09-01T17:25:15.189+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lwxun6hm89.png
[2025-09-01T17:25:15.189+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png
[2025-09-01T17:25:15.251+08:00] adb shell rm /data/local/tmp/midscene_screenshot_ea1b05c1-9635-46f5-acdf-f826001a0ec1.png end
[2025-09-01T17:25:15.251+08:00] Resizing screenshot image
[2025-09-01T17:25:15.364+08:00] Image resize completed
[2025-09-01T17:25:15.364+08:00] Converting to base64
[2025-09-01T17:25:15.364+08:00] screenshotBase64 end
[2025-09-01T17:25:15.364+08:00] adb shell wm,size
[2025-09-01T17:25:15.446+08:00] adb shell wm,size end
[2025-09-01T17:25:15.446+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:15.446+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:15.537+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:15.537+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:15.537+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:15.672+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:15.672+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:15.672+08:00] adb getScreenDensity 
[2025-09-01T17:25:15.773+08:00] adb getScreenDensity  end
[2025-09-01T17:25:15.774+08:00] adb shell input swipe 1268 2481 1268 2481 150
[2025-09-01T17:25:16.032+08:00] adb shell input swipe 1268 2481 1268 2481 150 end
[2025-09-01T17:25:16.242+08:00] screenshotBase64 begin
[2025-09-01T17:25:16.242+08:00] adb shell wm,size
[2025-09-01T17:25:16.348+08:00] adb shell wm,size end
[2025-09-01T17:25:16.348+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:16.348+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:16.430+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:16.430+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:16.430+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:16.580+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:16.580+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:16.580+08:00] adb getScreenDensity 
[2025-09-01T17:25:16.725+08:00] adb getScreenDensity  end
[2025-09-01T17:25:16.725+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:16.725+08:00] adb takeScreenshot 
[2025-09-01T17:25:16.933+08:00] adb takeScreenshot  end
[2025-09-01T17:25:16.933+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:16.933+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:16.933+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:16.933+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:16.933+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png
[2025-09-01T17:25:17.539+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png end
[2025-09-01T17:25:17.539+08:00] adb.shell screencap completed
[2025-09-01T17:25:17.539+08:00] Pulling screenshot file from device
[2025-09-01T17:25:17.539+08:00] adb pull /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ny926utiuw.png
[2025-09-01T17:25:17.599+08:00] adb pull /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ny926utiuw.png end
[2025-09-01T17:25:17.599+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ny926utiuw.png
[2025-09-01T17:25:17.600+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png
[2025-09-01T17:25:17.661+08:00] adb shell rm /data/local/tmp/midscene_screenshot_4c25eb4b-59ce-4ed3-a5f5-15559cafa675.png end
[2025-09-01T17:25:17.661+08:00] Resizing screenshot image
[2025-09-01T17:25:17.787+08:00] Image resize completed
[2025-09-01T17:25:17.787+08:00] Converting to base64
[2025-09-01T17:25:17.787+08:00] screenshotBase64 end
[2025-09-01T17:25:17.850+08:00] screenshotBase64 begin
[2025-09-01T17:25:17.850+08:00] adb shell wm,size
[2025-09-01T17:25:17.927+08:00] adb shell wm,size end
[2025-09-01T17:25:17.928+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:17.928+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:18.007+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:18.007+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:18.007+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:18.130+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:18.130+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:18.130+08:00] adb getScreenDensity 
[2025-09-01T17:25:18.254+08:00] adb getScreenDensity  end
[2025-09-01T17:25:18.254+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:18.254+08:00] adb takeScreenshot 
[2025-09-01T17:25:18.445+08:00] adb takeScreenshot  end
[2025-09-01T17:25:18.445+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:18.445+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:18.445+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:18.446+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:18.446+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png
[2025-09-01T17:25:19.035+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png end
[2025-09-01T17:25:19.035+08:00] adb.shell screencap completed
[2025-09-01T17:25:19.035+08:00] Pulling screenshot file from device
[2025-09-01T17:25:19.035+08:00] adb pull /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9ih1oxf0ih7.png
[2025-09-01T17:25:19.100+08:00] adb pull /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9ih1oxf0ih7.png end
[2025-09-01T17:25:19.100+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\9ih1oxf0ih7.png
[2025-09-01T17:25:19.102+08:00] adb shell rm /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png
[2025-09-01T17:25:19.171+08:00] adb shell rm /data/local/tmp/midscene_screenshot_58826b8f-6635-46c9-8576-105a691915a5.png end
[2025-09-01T17:25:19.171+08:00] Resizing screenshot image
[2025-09-01T17:25:19.297+08:00] Image resize completed
[2025-09-01T17:25:19.297+08:00] Converting to base64
[2025-09-01T17:25:19.297+08:00] screenshotBase64 end
[2025-09-01T17:25:19.297+08:00] adb shell wm,size
[2025-09-01T17:25:19.391+08:00] adb shell wm,size end
[2025-09-01T17:25:19.391+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:19.391+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:19.481+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:19.481+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:19.481+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:19.624+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:19.624+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:19.624+08:00] adb getScreenDensity 
[2025-09-01T17:25:19.737+08:00] adb getScreenDensity  end
[2025-09-01T17:25:22.699+08:00] screenshotBase64 begin
[2025-09-01T17:25:22.699+08:00] adb shell wm,size
[2025-09-01T17:25:22.794+08:00] adb shell wm,size end
[2025-09-01T17:25:22.794+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:22.794+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:22.918+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:22.918+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:22.918+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:23.038+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:23.039+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:23.039+08:00] adb getScreenDensity 
[2025-09-01T17:25:23.136+08:00] adb getScreenDensity  end
[2025-09-01T17:25:23.136+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:23.136+08:00] adb takeScreenshot 
[2025-09-01T17:25:23.318+08:00] adb takeScreenshot  end
[2025-09-01T17:25:23.318+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:23.318+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:23.318+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:23.319+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:23.319+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png
[2025-09-01T17:25:23.919+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png end
[2025-09-01T17:25:23.919+08:00] adb.shell screencap completed
[2025-09-01T17:25:23.919+08:00] Pulling screenshot file from device
[2025-09-01T17:25:23.919+08:00] adb pull /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dablx5jkaju.png
[2025-09-01T17:25:23.989+08:00] adb pull /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dablx5jkaju.png end
[2025-09-01T17:25:23.989+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\dablx5jkaju.png
[2025-09-01T17:25:23.990+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png
[2025-09-01T17:25:24.059+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1a8e1cb1-5203-4461-89aa-c9de26450a7a.png end
[2025-09-01T17:25:24.059+08:00] Resizing screenshot image
[2025-09-01T17:25:24.164+08:00] Image resize completed
[2025-09-01T17:25:24.164+08:00] Converting to base64
[2025-09-01T17:25:24.164+08:00] screenshotBase64 end
[2025-09-01T17:25:24.240+08:00] screenshotBase64 begin
[2025-09-01T17:25:24.240+08:00] adb shell wm,size
[2025-09-01T17:25:24.351+08:00] adb shell wm,size end
[2025-09-01T17:25:24.351+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:24.351+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:24.440+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:24.440+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:24.440+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:24.580+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:24.580+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:24.580+08:00] adb getScreenDensity 
[2025-09-01T17:25:24.669+08:00] adb getScreenDensity  end
[2025-09-01T17:25:24.669+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:24.669+08:00] adb takeScreenshot 
[2025-09-01T17:25:24.853+08:00] adb takeScreenshot  end
[2025-09-01T17:25:24.853+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:24.853+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:24.853+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:24.853+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:24.853+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png
[2025-09-01T17:25:25.436+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png end
[2025-09-01T17:25:25.436+08:00] adb.shell screencap completed
[2025-09-01T17:25:25.436+08:00] Pulling screenshot file from device
[2025-09-01T17:25:25.436+08:00] adb pull /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\96k3gifzzaw.png
[2025-09-01T17:25:25.498+08:00] adb pull /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\96k3gifzzaw.png end
[2025-09-01T17:25:25.498+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\96k3gifzzaw.png
[2025-09-01T17:25:25.499+08:00] adb shell rm /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png
[2025-09-01T17:25:25.570+08:00] adb shell rm /data/local/tmp/midscene_screenshot_02df24d5-aca5-49c2-8dd9-f5c23dd9099f.png end
[2025-09-01T17:25:25.570+08:00] Resizing screenshot image
[2025-09-01T17:25:25.707+08:00] Image resize completed
[2025-09-01T17:25:25.707+08:00] Converting to base64
[2025-09-01T17:25:25.707+08:00] screenshotBase64 end
[2025-09-01T17:25:25.707+08:00] adb shell wm,size
[2025-09-01T17:25:25.816+08:00] adb shell wm,size end
[2025-09-01T17:25:25.816+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:25.816+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:25.915+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:25.915+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:25.915+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:26.042+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:26.042+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:26.042+08:00] adb getScreenDensity 
[2025-09-01T17:25:26.125+08:00] adb getScreenDensity  end
[2025-09-01T17:25:26.126+08:00] adb inputText what\'s the weather today
[2025-09-01T17:25:27.269+08:00] adb inputText what\'s the weather today end
[2025-09-01T17:25:27.269+08:00] adb isSoftKeyboardPresent 
[2025-09-01T17:25:27.449+08:00] adb isSoftKeyboardPresent  end
[2025-09-01T17:25:27.449+08:00] adb keyevent 111
[2025-09-01T17:25:27.537+08:00] adb keyevent 111 end
[2025-09-01T17:25:27.652+08:00] adb isSoftKeyboardPresent 
[2025-09-01T17:25:27.802+08:00] adb isSoftKeyboardPresent  end
[2025-09-01T17:25:27.903+08:00] adb isSoftKeyboardPresent 
[2025-09-01T17:25:28.082+08:00] adb isSoftKeyboardPresent  end
[2025-09-01T17:25:28.082+08:00] Keyboard hidden successfully with keycode 111
[2025-09-01T17:25:28.291+08:00] screenshotBase64 begin
[2025-09-01T17:25:28.291+08:00] adb shell wm,size
[2025-09-01T17:25:28.380+08:00] adb shell wm,size end
[2025-09-01T17:25:28.381+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:28.381+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:28.461+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:28.461+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:28.461+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:28.581+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:28.581+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:28.581+08:00] adb getScreenDensity 
[2025-09-01T17:25:28.668+08:00] adb getScreenDensity  end
[2025-09-01T17:25:28.669+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:28.669+08:00] adb takeScreenshot 
[2025-09-01T17:25:28.881+08:00] adb takeScreenshot  end
[2025-09-01T17:25:28.881+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:28.881+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:28.881+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:28.882+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:28.882+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png
[2025-09-01T17:25:29.419+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png end
[2025-09-01T17:25:29.420+08:00] adb.shell screencap completed
[2025-09-01T17:25:29.420+08:00] Pulling screenshot file from device
[2025-09-01T17:25:29.420+08:00] adb pull /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ku9q771t89r.png
[2025-09-01T17:25:29.487+08:00] adb pull /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ku9q771t89r.png end
[2025-09-01T17:25:29.487+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\ku9q771t89r.png
[2025-09-01T17:25:29.488+08:00] adb shell rm /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png
[2025-09-01T17:25:29.561+08:00] adb shell rm /data/local/tmp/midscene_screenshot_72e7d184-eb38-4cec-9a35-1aa20f7cef39.png end
[2025-09-01T17:25:29.561+08:00] Resizing screenshot image
[2025-09-01T17:25:29.679+08:00] Image resize completed
[2025-09-01T17:25:29.679+08:00] Converting to base64
[2025-09-01T17:25:29.680+08:00] screenshotBase64 end
[2025-09-01T17:25:29.759+08:00] screenshotBase64 begin
[2025-09-01T17:25:29.759+08:00] adb shell wm,size
[2025-09-01T17:25:29.855+08:00] adb shell wm,size end
[2025-09-01T17:25:29.855+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:29.855+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:29.953+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:29.953+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:29.953+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:30.095+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:30.095+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:30.095+08:00] adb getScreenDensity 
[2025-09-01T17:25:30.219+08:00] adb getScreenDensity  end
[2025-09-01T17:25:30.219+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:30.219+08:00] adb takeScreenshot 
[2025-09-01T17:25:30.432+08:00] adb takeScreenshot  end
[2025-09-01T17:25:30.432+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:30.432+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:30.432+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:30.432+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:30.432+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png
[2025-09-01T17:25:30.963+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png end
[2025-09-01T17:25:30.963+08:00] adb.shell screencap completed
[2025-09-01T17:25:30.963+08:00] Pulling screenshot file from device
[2025-09-01T17:25:30.963+08:00] adb pull /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qyktlslrzl.png
[2025-09-01T17:25:31.007+08:00] adb pull /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qyktlslrzl.png end
[2025-09-01T17:25:31.007+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\qyktlslrzl.png
[2025-09-01T17:25:31.008+08:00] adb shell rm /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png
[2025-09-01T17:25:31.073+08:00] adb shell rm /data/local/tmp/midscene_screenshot_956434fb-aee9-4d5d-8451-48ca564b87d1.png end
[2025-09-01T17:25:31.074+08:00] Resizing screenshot image
[2025-09-01T17:25:31.194+08:00] Image resize completed
[2025-09-01T17:25:31.194+08:00] Converting to base64
[2025-09-01T17:25:31.194+08:00] screenshotBase64 end
[2025-09-01T17:25:31.194+08:00] adb shell wm,size
[2025-09-01T17:25:31.277+08:00] adb shell wm,size end
[2025-09-01T17:25:31.277+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:31.277+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:31.380+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:31.380+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:31.380+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:31.515+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:31.515+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:31.515+08:00] adb getScreenDensity 
[2025-09-01T17:25:31.607+08:00] adb getScreenDensity  end
[2025-09-01T17:25:34.092+08:00] screenshotBase64 begin
[2025-09-01T17:25:34.092+08:00] adb shell wm,size
[2025-09-01T17:25:34.172+08:00] adb shell wm,size end
[2025-09-01T17:25:34.172+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:34.172+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:34.269+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:34.269+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:34.269+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:34.383+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:34.383+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:34.383+08:00] adb getScreenDensity 
[2025-09-01T17:25:34.486+08:00] adb getScreenDensity  end
[2025-09-01T17:25:34.486+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:34.486+08:00] adb takeScreenshot 
[2025-09-01T17:25:34.668+08:00] adb takeScreenshot  end
[2025-09-01T17:25:34.668+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:34.668+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:34.668+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:34.668+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:34.668+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png
[2025-09-01T17:25:35.255+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png end
[2025-09-01T17:25:35.255+08:00] adb.shell screencap completed
[2025-09-01T17:25:35.255+08:00] Pulling screenshot file from device
[2025-09-01T17:25:35.255+08:00] adb pull /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kou0hlk3mki.png
[2025-09-01T17:25:35.309+08:00] adb pull /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kou0hlk3mki.png end
[2025-09-01T17:25:35.309+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\kou0hlk3mki.png
[2025-09-01T17:25:35.310+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png
[2025-09-01T17:25:35.383+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a6923ea8-896a-4bbe-b957-8def012b7766.png end
[2025-09-01T17:25:35.383+08:00] Resizing screenshot image
[2025-09-01T17:25:35.497+08:00] Image resize completed
[2025-09-01T17:25:35.497+08:00] Converting to base64
[2025-09-01T17:25:35.498+08:00] screenshotBase64 end
[2025-09-01T17:25:36.708+08:00] screenshotBase64 begin
[2025-09-01T17:25:36.708+08:00] adb shell wm,size
[2025-09-01T17:25:36.820+08:00] adb shell wm,size end
[2025-09-01T17:25:36.820+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:36.820+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:36.912+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:36.912+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:36.913+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:37.025+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:37.025+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:37.025+08:00] adb getScreenDensity 
[2025-09-01T17:25:37.121+08:00] adb getScreenDensity  end
[2025-09-01T17:25:37.121+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:37.121+08:00] adb takeScreenshot 
[2025-09-01T17:25:37.304+08:00] adb takeScreenshot  end
[2025-09-01T17:25:37.304+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:37.304+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:37.304+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:37.305+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:37.305+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png
[2025-09-01T17:25:37.889+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png end
[2025-09-01T17:25:37.889+08:00] adb.shell screencap completed
[2025-09-01T17:25:37.889+08:00] Pulling screenshot file from device
[2025-09-01T17:25:37.889+08:00] adb pull /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\sd3gqsrrr9.png
[2025-09-01T17:25:37.946+08:00] adb pull /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\sd3gqsrrr9.png end
[2025-09-01T17:25:37.946+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\sd3gqsrrr9.png
[2025-09-01T17:25:37.948+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png
[2025-09-01T17:25:38.012+08:00] adb shell rm /data/local/tmp/midscene_screenshot_1e070e62-f21c-4dcf-8684-927f060595ad.png end
[2025-09-01T17:25:38.012+08:00] Resizing screenshot image
[2025-09-01T17:25:38.142+08:00] Image resize completed
[2025-09-01T17:25:38.142+08:00] Converting to base64
[2025-09-01T17:25:38.143+08:00] screenshotBase64 end
[2025-09-01T17:25:38.203+08:00] screenshotBase64 begin
[2025-09-01T17:25:38.203+08:00] adb shell wm,size
[2025-09-01T17:25:38.302+08:00] adb shell wm,size end
[2025-09-01T17:25:38.302+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:38.302+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:38.398+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:38.399+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:38.399+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:38.513+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:38.513+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:38.513+08:00] adb getScreenDensity 
[2025-09-01T17:25:38.610+08:00] adb getScreenDensity  end
[2025-09-01T17:25:38.610+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:38.610+08:00] adb takeScreenshot 
[2025-09-01T17:25:38.803+08:00] adb takeScreenshot  end
[2025-09-01T17:25:38.803+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:38.803+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:38.803+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:38.804+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:38.804+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png
[2025-09-01T17:25:39.419+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png end
[2025-09-01T17:25:39.419+08:00] adb.shell screencap completed
[2025-09-01T17:25:39.419+08:00] Pulling screenshot file from device
[2025-09-01T17:25:39.419+08:00] adb pull /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\132vsp92op9h.png
[2025-09-01T17:25:39.478+08:00] adb pull /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\132vsp92op9h.png end
[2025-09-01T17:25:39.479+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\132vsp92op9h.png
[2025-09-01T17:25:39.479+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png
[2025-09-01T17:25:39.550+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9aadf152-b4fc-44c8-83f6-e717da72b3b8.png end
[2025-09-01T17:25:39.550+08:00] Resizing screenshot image
[2025-09-01T17:25:39.673+08:00] Image resize completed
[2025-09-01T17:25:39.673+08:00] Converting to base64
[2025-09-01T17:25:39.673+08:00] screenshotBase64 end
[2025-09-01T17:25:39.673+08:00] adb shell wm,size
[2025-09-01T17:25:39.765+08:00] adb shell wm,size end
[2025-09-01T17:25:39.765+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:39.765+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:39.870+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:39.870+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:39.870+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:40.000+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:40.000+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:40.000+08:00] adb getScreenDensity 
[2025-09-01T17:25:40.114+08:00] adb getScreenDensity  end
[2025-09-01T17:25:43.351+08:00] screenshotBase64 begin
[2025-09-01T17:25:43.351+08:00] adb shell wm,size
[2025-09-01T17:25:43.438+08:00] adb shell wm,size end
[2025-09-01T17:25:43.438+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:43.438+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:43.531+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:43.531+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:43.531+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:43.673+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:43.673+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:43.673+08:00] adb getScreenDensity 
[2025-09-01T17:25:43.760+08:00] adb getScreenDensity  end
[2025-09-01T17:25:43.761+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:43.761+08:00] adb takeScreenshot 
[2025-09-01T17:25:43.956+08:00] adb takeScreenshot  end
[2025-09-01T17:25:43.956+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:43.956+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:43.956+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:43.956+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:43.956+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png
[2025-09-01T17:25:44.555+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png end
[2025-09-01T17:25:44.556+08:00] adb.shell screencap completed
[2025-09-01T17:25:44.556+08:00] Pulling screenshot file from device
[2025-09-01T17:25:44.556+08:00] adb pull /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y3ndxr3j0si.png
[2025-09-01T17:25:44.619+08:00] adb pull /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y3ndxr3j0si.png end
[2025-09-01T17:25:44.620+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\y3ndxr3j0si.png
[2025-09-01T17:25:44.620+08:00] adb shell rm /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png
[2025-09-01T17:25:44.685+08:00] adb shell rm /data/local/tmp/midscene_screenshot_495a6e9b-067f-4c2c-8fcf-4c16998bbb12.png end
[2025-09-01T17:25:44.685+08:00] Resizing screenshot image
[2025-09-01T17:25:44.798+08:00] Image resize completed
[2025-09-01T17:25:44.798+08:00] Converting to base64
[2025-09-01T17:25:44.798+08:00] screenshotBase64 end
[2025-09-01T17:25:44.799+08:00] adb shell wm,size
[2025-09-01T17:25:44.886+08:00] adb shell wm,size end
[2025-09-01T17:25:44.886+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:44.886+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:44.990+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:44.990+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:44.990+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:45.129+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:45.129+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:45.129+08:00] adb getScreenDensity 
[2025-09-01T17:25:45.242+08:00] adb getScreenDensity  end
[2025-09-01T17:25:45.243+08:00] screenshotBase64 begin
[2025-09-01T17:25:45.243+08:00] adb shell wm,size
[2025-09-01T17:25:45.342+08:00] adb shell wm,size end
[2025-09-01T17:25:45.342+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:45.342+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:45.436+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:45.437+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:45.437+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:45.588+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:45.588+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:45.588+08:00] adb getScreenDensity 
[2025-09-01T17:25:45.672+08:00] adb getScreenDensity  end
[2025-09-01T17:25:45.672+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:45.672+08:00] adb takeScreenshot 
[2025-09-01T17:25:45.863+08:00] adb takeScreenshot  end
[2025-09-01T17:25:45.864+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:45.864+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:45.864+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:45.864+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:45.864+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png
[2025-09-01T17:25:46.469+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png end
[2025-09-01T17:25:46.469+08:00] adb.shell screencap completed
[2025-09-01T17:25:46.469+08:00] Pulling screenshot file from device
[2025-09-01T17:25:46.469+08:00] adb pull /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\d5fu6703gp.png
[2025-09-01T17:25:46.526+08:00] adb pull /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\d5fu6703gp.png end
[2025-09-01T17:25:46.526+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\d5fu6703gp.png
[2025-09-01T17:25:46.527+08:00] adb shell rm /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png
[2025-09-01T17:25:46.590+08:00] adb shell rm /data/local/tmp/midscene_screenshot_48df0d2b-8cf5-4526-883b-2afa1f933c43.png end
[2025-09-01T17:25:46.590+08:00] Resizing screenshot image
[2025-09-01T17:25:46.707+08:00] Image resize completed
[2025-09-01T17:25:46.707+08:00] Converting to base64
[2025-09-01T17:25:46.707+08:00] screenshotBase64 end
[2025-09-01T17:25:46.768+08:00] screenshotBase64 begin
[2025-09-01T17:25:46.768+08:00] adb shell wm,size
[2025-09-01T17:25:46.862+08:00] adb shell wm,size end
[2025-09-01T17:25:46.862+08:00] Using Physical size: 1080x2436
[2025-09-01T17:25:46.862+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:25:46.958+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:25:46.958+08:00] Failed to get orientation from input, try display
[2025-09-01T17:25:46.958+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:25:47.081+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:25:47.082+08:00] Screen orientation (fallback): 0
[2025-09-01T17:25:47.082+08:00] adb getScreenDensity 
[2025-09-01T17:25:47.170+08:00] adb getScreenDensity  end
[2025-09-01T17:25:47.170+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:25:47.170+08:00] adb takeScreenshot 
[2025-09-01T17:25:47.370+08:00] adb takeScreenshot  end
[2025-09-01T17:25:47.370+08:00] adb.takeScreenshot completed
[2025-09-01T17:25:47.370+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:25:47.370+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:25:47.370+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:25:47.370+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png
[2025-09-01T17:25:47.699+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'screencap -p  /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png'' exited with code 3221225786'; Command output: <empty>
[2025-09-01T17:25:47.700+08:00] screencap failed, using forceScreenshot
[2025-09-01T17:25:47.701+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp
[2025-09-01T17:25:47.825+08:00] adb push D:\aigc\aigc_ui_tools\node_modules\@midscene\android\bin\yadb /data/local/tmp end
[2025-09-01T17:25:47.825+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -screenshot /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png
[2025-09-01T17:25:49.052+08:00] adb shell app_process -Djava.class.path=/data/local/tmp/yadb /data/local/tmp com.ysbing.yadb.Main -screenshot /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png end
[2025-09-01T17:25:49.052+08:00] forceScreenshot completed
[2025-09-01T17:25:49.052+08:00] Pulling screenshot file from device
[2025-09-01T17:25:49.052+08:00] adb pull /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\wpkor039td.png
[2025-09-01T17:25:49.102+08:00] adb pull /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\wpkor039td.png end
[2025-09-01T17:25:49.102+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\wpkor039td.png
[2025-09-01T17:25:49.103+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png
[2025-09-01T17:25:49.170+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d5a5e5ce-7488-42a7-8980-bb60d9ab7832.png end
[2025-09-01T17:25:49.170+08:00] Resizing screenshot image
[2025-09-01T17:26:13.008+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-09-01T17:26:13.009+08:00] adb shell wm,size
[2025-09-01T17:26:13.084+08:00] adb shell wm,size end
[2025-09-01T17:26:13.084+08:00] Using Physical size: 1080x2436
[2025-09-01T17:26:13.084+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:26:13.154+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:26:13.155+08:00] Failed to get orientation from input, try display
[2025-09-01T17:26:13.155+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:26:13.245+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:26:13.245+08:00] Screen orientation (fallback): 0
[2025-09-01T17:26:13.245+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-09-01T17:26:13.245+08:00] Launching app: com.transsion.aivoiceassistant
[2025-09-01T17:26:13.245+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-09-01T17:26:13.566+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-09-01T17:26:13.566+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-09-01T17:26:13.619+08:00] screenshotBase64 begin
[2025-09-01T17:26:13.620+08:00] adb shell wm,size
[2025-09-01T17:26:13.774+08:00] adb shell wm,size end
[2025-09-01T17:26:13.774+08:00] Using Physical size: 1080x2436
[2025-09-01T17:26:13.774+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:26:13.867+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:26:13.867+08:00] Failed to get orientation from input, try display
[2025-09-01T17:26:13.867+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:26:14.235+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:26:14.235+08:00] Screen orientation (fallback): 0
[2025-09-01T17:26:14.235+08:00] adb getScreenDensity 
[2025-09-01T17:26:14.337+08:00] adb getScreenDensity  end
[2025-09-01T17:26:14.337+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:26:14.337+08:00] adb takeScreenshot 
[2025-09-01T17:26:14.569+08:00] adb takeScreenshot  end
[2025-09-01T17:26:14.569+08:00] adb.takeScreenshot completed
[2025-09-01T17:26:14.569+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:26:14.569+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:26:14.570+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:26:14.570+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png
[2025-09-01T17:26:15.245+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png end
[2025-09-01T17:26:15.245+08:00] adb.shell screencap completed
[2025-09-01T17:26:15.245+08:00] Pulling screenshot file from device
[2025-09-01T17:26:15.245+08:00] adb pull /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\l3s8uenlgys.png
[2025-09-01T17:26:15.307+08:00] adb pull /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\l3s8uenlgys.png end
[2025-09-01T17:26:15.307+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\l3s8uenlgys.png
[2025-09-01T17:26:15.308+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png
[2025-09-01T17:26:15.368+08:00] adb shell rm /data/local/tmp/midscene_screenshot_a8fbcfd2-26e5-4382-b100-f5ad37de646f.png end
[2025-09-01T17:26:15.368+08:00] Resizing screenshot image
[2025-09-01T17:26:15.581+08:00] Image resize completed
[2025-09-01T17:26:15.581+08:00] Converting to base64
[2025-09-01T17:26:15.581+08:00] screenshotBase64 end
[2025-09-01T17:26:15.581+08:00] adb shell wm,size
[2025-09-01T17:26:15.672+08:00] adb shell wm,size end
[2025-09-01T17:26:15.672+08:00] Using Physical size: 1080x2436
[2025-09-01T17:26:15.672+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:26:15.775+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:26:15.775+08:00] Failed to get orientation from input, try display
[2025-09-01T17:26:15.775+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:26:15.892+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:26:15.892+08:00] Screen orientation (fallback): 0
[2025-09-01T17:26:15.892+08:00] adb getScreenDensity 
[2025-09-01T17:26:15.989+08:00] adb getScreenDensity  end
[2025-09-01T17:27:13.479+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-09-01T17:27:13.480+08:00] adb shell wm,size
[2025-09-01T17:27:13.574+08:00] adb shell wm,size end
[2025-09-01T17:27:13.574+08:00] Using Physical size: 1080x2436
[2025-09-01T17:27:13.575+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:27:13.665+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:27:13.665+08:00] Failed to get orientation from input, try display
[2025-09-01T17:27:13.665+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:27:13.789+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:27:13.789+08:00] Screen orientation (fallback): 0
[2025-09-01T17:27:13.789+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-09-01T17:27:13.789+08:00] Launching app: com.transsion.aivoiceassistant
[2025-09-01T17:27:13.789+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-09-01T17:27:14.086+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-09-01T17:27:14.086+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-09-01T17:27:14.168+08:00] screenshotBase64 begin
[2025-09-01T17:27:14.168+08:00] adb shell wm,size
[2025-09-01T17:27:14.247+08:00] adb shell wm,size end
[2025-09-01T17:27:14.247+08:00] Using Physical size: 1080x2436
[2025-09-01T17:27:14.247+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:27:14.333+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:27:14.333+08:00] Failed to get orientation from input, try display
[2025-09-01T17:27:14.333+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:27:14.430+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:27:14.430+08:00] Screen orientation (fallback): 0
[2025-09-01T17:27:14.430+08:00] adb getScreenDensity 
[2025-09-01T17:27:14.512+08:00] adb getScreenDensity  end
[2025-09-01T17:27:14.513+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:27:14.513+08:00] adb takeScreenshot 
[2025-09-01T17:27:14.700+08:00] adb takeScreenshot  end
[2025-09-01T17:27:14.700+08:00] adb.takeScreenshot completed
[2025-09-01T17:27:14.700+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:27:14.700+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:27:14.701+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:27:14.701+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png
[2025-09-01T17:27:15.292+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png end
[2025-09-01T17:27:15.292+08:00] adb.shell screencap completed
[2025-09-01T17:27:15.292+08:00] Pulling screenshot file from device
[2025-09-01T17:27:15.292+08:00] adb pull /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\h6xlvk1reqh.png
[2025-09-01T17:27:15.353+08:00] adb pull /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\h6xlvk1reqh.png end
[2025-09-01T17:27:15.353+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\h6xlvk1reqh.png
[2025-09-01T17:27:15.353+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png
[2025-09-01T17:27:15.413+08:00] adb shell rm /data/local/tmp/midscene_screenshot_0245116e-2448-4117-9269-8fe78d5ad1d9.png end
[2025-09-01T17:27:15.413+08:00] Resizing screenshot image
[2025-09-01T17:27:15.605+08:00] Image resize completed
[2025-09-01T17:27:15.605+08:00] Converting to base64
[2025-09-01T17:27:15.606+08:00] screenshotBase64 end
[2025-09-01T17:27:15.606+08:00] adb shell wm,size
[2025-09-01T17:27:15.698+08:00] adb shell wm,size end
[2025-09-01T17:27:15.698+08:00] Using Physical size: 1080x2436
[2025-09-01T17:27:15.698+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:27:15.801+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:27:15.801+08:00] Failed to get orientation from input, try display
[2025-09-01T17:27:15.801+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:27:15.917+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:27:15.917+08:00] Screen orientation (fallback): 0
[2025-09-01T17:27:15.917+08:00] adb getScreenDensity 
[2025-09-01T17:27:16.014+08:00] adb getScreenDensity  end
[2025-09-01T17:28:22.972+08:00] Initializing ADB with device ID: ASALE3741B000022
[2025-09-01T17:28:22.974+08:00] adb shell wm,size
[2025-09-01T17:28:23.085+08:00] adb shell wm,size end
[2025-09-01T17:28:23.085+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:23.085+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:23.190+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:23.191+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:23.191+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:23.340+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:23.340+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:23.340+08:00] ADB initialized successfully 
DeviceId: ASALE3741B000022
ScreenSize:
  physical size: 1080x2436

[2025-09-01T17:28:23.340+08:00] Launching app: com.transsion.aivoiceassistant
[2025-09-01T17:28:23.340+08:00] adb activateApp com.transsion.aivoiceassistant
[2025-09-01T17:28:23.660+08:00] adb activateApp com.transsion.aivoiceassistant end
[2025-09-01T17:28:23.660+08:00] Successfully launched: com.transsion.aivoiceassistant
[2025-09-01T17:28:23.738+08:00] screenshotBase64 begin
[2025-09-01T17:28:23.738+08:00] adb shell wm,size
[2025-09-01T17:28:23.863+08:00] adb shell wm,size end
[2025-09-01T17:28:23.863+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:23.863+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:23.972+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:23.972+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:23.972+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:24.320+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:24.320+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:24.320+08:00] adb getScreenDensity 
[2025-09-01T17:28:24.417+08:00] adb getScreenDensity  end
[2025-09-01T17:28:24.417+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:24.417+08:00] adb takeScreenshot 
[2025-09-01T17:28:24.639+08:00] adb takeScreenshot  end
[2025-09-01T17:28:24.639+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:24.639+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:24.639+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:24.640+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:24.640+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png
[2025-09-01T17:28:25.329+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png end
[2025-09-01T17:28:25.329+08:00] adb.shell screencap completed
[2025-09-01T17:28:25.329+08:00] Pulling screenshot file from device
[2025-09-01T17:28:25.329+08:00] adb pull /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\0f0ovn4ru8r.png
[2025-09-01T17:28:25.393+08:00] adb pull /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\0f0ovn4ru8r.png end
[2025-09-01T17:28:25.393+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\0f0ovn4ru8r.png
[2025-09-01T17:28:25.394+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png
[2025-09-01T17:28:25.475+08:00] adb shell rm /data/local/tmp/midscene_screenshot_dfd30bab-1589-4d36-9475-abda770007da.png end
[2025-09-01T17:28:25.475+08:00] Resizing screenshot image
[2025-09-01T17:28:25.680+08:00] Image resize completed
[2025-09-01T17:28:25.680+08:00] Converting to base64
[2025-09-01T17:28:25.680+08:00] screenshotBase64 end
[2025-09-01T17:28:25.680+08:00] adb shell wm,size
[2025-09-01T17:28:25.788+08:00] adb shell wm,size end
[2025-09-01T17:28:25.789+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:25.789+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:25.908+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:25.909+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:25.909+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:26.031+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:26.031+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:26.032+08:00] adb getScreenDensity 
[2025-09-01T17:28:26.134+08:00] adb getScreenDensity  end
[2025-09-01T17:28:31.037+08:00] screenshotBase64 begin
[2025-09-01T17:28:31.037+08:00] adb shell wm,size
[2025-09-01T17:28:31.149+08:00] adb shell wm,size end
[2025-09-01T17:28:31.149+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:31.149+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:31.262+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:31.262+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:31.262+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:31.408+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:31.408+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:31.408+08:00] adb getScreenDensity 
[2025-09-01T17:28:31.534+08:00] adb getScreenDensity  end
[2025-09-01T17:28:31.534+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:31.534+08:00] adb takeScreenshot 
[2025-09-01T17:28:31.739+08:00] adb takeScreenshot  end
[2025-09-01T17:28:31.739+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:31.739+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:31.740+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:31.741+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:31.741+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png
[2025-09-01T17:28:32.354+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png end
[2025-09-01T17:28:32.354+08:00] adb.shell screencap completed
[2025-09-01T17:28:32.354+08:00] Pulling screenshot file from device
[2025-09-01T17:28:32.354+08:00] adb pull /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cd9yb21x7ft.png
[2025-09-01T17:28:32.420+08:00] adb pull /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cd9yb21x7ft.png end
[2025-09-01T17:28:32.420+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\cd9yb21x7ft.png
[2025-09-01T17:28:32.420+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png
[2025-09-01T17:28:32.493+08:00] adb shell rm /data/local/tmp/midscene_screenshot_6ee404f6-c17d-422e-bca1-95e3b207894f.png end
[2025-09-01T17:28:32.493+08:00] Resizing screenshot image
[2025-09-01T17:28:32.630+08:00] Image resize completed
[2025-09-01T17:28:32.630+08:00] Converting to base64
[2025-09-01T17:28:32.630+08:00] screenshotBase64 end
[2025-09-01T17:28:32.840+08:00] screenshotBase64 begin
[2025-09-01T17:28:32.841+08:00] adb shell wm,size
[2025-09-01T17:28:32.942+08:00] adb shell wm,size end
[2025-09-01T17:28:32.942+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:32.942+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:33.027+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:33.027+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:33.027+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:33.145+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:33.145+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:33.145+08:00] adb getScreenDensity 
[2025-09-01T17:28:33.246+08:00] adb getScreenDensity  end
[2025-09-01T17:28:33.247+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:33.247+08:00] adb takeScreenshot 
[2025-09-01T17:28:33.460+08:00] adb takeScreenshot  end
[2025-09-01T17:28:33.460+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:33.460+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:33.460+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:33.460+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:33.460+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png
[2025-09-01T17:28:34.060+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png end
[2025-09-01T17:28:34.060+08:00] adb.shell screencap completed
[2025-09-01T17:28:34.060+08:00] Pulling screenshot file from device
[2025-09-01T17:28:34.060+08:00] adb pull /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3vnmlmwxust.png
[2025-09-01T17:28:34.128+08:00] adb pull /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3vnmlmwxust.png end
[2025-09-01T17:28:34.129+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\3vnmlmwxust.png
[2025-09-01T17:28:34.130+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png
[2025-09-01T17:28:34.197+08:00] adb shell rm /data/local/tmp/midscene_screenshot_d97c01d2-dc09-4860-a083-01651534b417.png end
[2025-09-01T17:28:34.197+08:00] Resizing screenshot image
[2025-09-01T17:28:34.329+08:00] Image resize completed
[2025-09-01T17:28:34.329+08:00] Converting to base64
[2025-09-01T17:28:34.329+08:00] screenshotBase64 end
[2025-09-01T17:28:34.406+08:00] screenshotBase64 begin
[2025-09-01T17:28:34.406+08:00] adb shell wm,size
[2025-09-01T17:28:34.510+08:00] adb shell wm,size end
[2025-09-01T17:28:34.510+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:34.511+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:34.613+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:34.613+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:34.613+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:34.747+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:34.747+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:34.747+08:00] adb getScreenDensity 
[2025-09-01T17:28:34.861+08:00] adb getScreenDensity  end
[2025-09-01T17:28:34.861+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:34.861+08:00] adb takeScreenshot 
[2025-09-01T17:28:35.070+08:00] adb takeScreenshot  end
[2025-09-01T17:28:35.070+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:35.070+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:35.070+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:35.071+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:35.071+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png
[2025-09-01T17:28:35.660+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png end
[2025-09-01T17:28:35.660+08:00] adb.shell screencap completed
[2025-09-01T17:28:35.660+08:00] Pulling screenshot file from device
[2025-09-01T17:28:35.660+08:00] adb pull /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lz78a78jhcl.png
[2025-09-01T17:28:35.753+08:00] adb pull /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lz78a78jhcl.png end
[2025-09-01T17:28:35.753+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\lz78a78jhcl.png
[2025-09-01T17:28:35.755+08:00] adb shell rm /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png
[2025-09-01T17:28:35.842+08:00] adb shell rm /data/local/tmp/midscene_screenshot_cf189de4-90c3-46e5-a13a-453745112799.png end
[2025-09-01T17:28:35.842+08:00] Resizing screenshot image
[2025-09-01T17:28:35.989+08:00] Image resize completed
[2025-09-01T17:28:35.989+08:00] Converting to base64
[2025-09-01T17:28:35.989+08:00] screenshotBase64 end
[2025-09-01T17:28:36.066+08:00] screenshotBase64 begin
[2025-09-01T17:28:36.066+08:00] adb shell wm,size
[2025-09-01T17:28:36.154+08:00] adb shell wm,size end
[2025-09-01T17:28:36.154+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:36.154+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:36.273+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:36.273+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:36.273+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:36.449+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:36.449+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:36.449+08:00] adb getScreenDensity 
[2025-09-01T17:28:36.560+08:00] adb getScreenDensity  end
[2025-09-01T17:28:36.560+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:36.560+08:00] adb takeScreenshot 
[2025-09-01T17:28:36.773+08:00] adb takeScreenshot  end
[2025-09-01T17:28:36.773+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:36.773+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:36.773+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:36.774+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:36.774+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png
[2025-09-01T17:28:37.383+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png end
[2025-09-01T17:28:37.383+08:00] adb.shell screencap completed
[2025-09-01T17:28:37.383+08:00] Pulling screenshot file from device
[2025-09-01T17:28:37.383+08:00] adb pull /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izlznjmy3.png
[2025-09-01T17:28:37.440+08:00] adb pull /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izlznjmy3.png end
[2025-09-01T17:28:37.440+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\izlznjmy3.png
[2025-09-01T17:28:37.440+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png
[2025-09-01T17:28:37.503+08:00] adb shell rm /data/local/tmp/midscene_screenshot_9fea1326-4ec1-4aab-8d99-ea6756593a2b.png end
[2025-09-01T17:28:37.503+08:00] Resizing screenshot image
[2025-09-01T17:28:37.663+08:00] Image resize completed
[2025-09-01T17:28:37.663+08:00] Converting to base64
[2025-09-01T17:28:37.664+08:00] screenshotBase64 end
[2025-09-01T17:28:37.664+08:00] adb shell wm,size
[2025-09-01T17:28:37.759+08:00] adb shell wm,size end
[2025-09-01T17:28:37.759+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:37.759+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:37.850+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:37.850+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:37.850+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:37.956+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:37.956+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:37.956+08:00] adb getScreenDensity 
[2025-09-01T17:28:38.058+08:00] adb getScreenDensity  end
[2025-09-01T17:28:38.143+08:00] screenshotBase64 begin
[2025-09-01T17:28:38.143+08:00] adb shell wm,size
[2025-09-01T17:28:38.266+08:00] adb shell wm,size end
[2025-09-01T17:28:38.266+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:38.266+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:38.381+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:38.381+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:38.381+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:38.494+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:38.494+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:38.494+08:00] adb getScreenDensity 
[2025-09-01T17:28:38.581+08:00] adb getScreenDensity  end
[2025-09-01T17:28:38.581+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:38.581+08:00] adb takeScreenshot 
[2025-09-01T17:28:38.775+08:00] adb takeScreenshot  end
[2025-09-01T17:28:38.775+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:38.775+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:38.775+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:38.776+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:38.776+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png
[2025-09-01T17:28:39.394+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png end
[2025-09-01T17:28:39.394+08:00] adb.shell screencap completed
[2025-09-01T17:28:39.394+08:00] Pulling screenshot file from device
[2025-09-01T17:28:39.394+08:00] adb pull /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\i60hidyx22p.png
[2025-09-01T17:28:39.459+08:00] adb pull /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\i60hidyx22p.png end
[2025-09-01T17:28:39.459+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\i60hidyx22p.png
[2025-09-01T17:28:39.460+08:00] adb shell rm /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png
[2025-09-01T17:28:39.533+08:00] adb shell rm /data/local/tmp/midscene_screenshot_122a4477-1450-43dc-acda-babba2413d71.png end
[2025-09-01T17:28:39.533+08:00] Resizing screenshot image
[2025-09-01T17:28:39.696+08:00] Image resize completed
[2025-09-01T17:28:39.696+08:00] Converting to base64
[2025-09-01T17:28:39.696+08:00] screenshotBase64 end
[2025-09-01T17:28:39.696+08:00] adb shell wm,size
[2025-09-01T17:28:39.795+08:00] adb shell wm,size end
[2025-09-01T17:28:39.796+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:39.796+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:39.903+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:39.903+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:39.903+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:40.062+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:40.062+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:40.062+08:00] adb getScreenDensity 
[2025-09-01T17:28:40.157+08:00] adb getScreenDensity  end
[2025-09-01T17:28:42.911+08:00] screenshotBase64 begin
[2025-09-01T17:28:42.911+08:00] adb shell wm,size
[2025-09-01T17:28:43.012+08:00] adb shell wm,size end
[2025-09-01T17:28:43.012+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:43.012+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:43.104+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:43.104+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:43.104+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:43.232+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:43.232+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:43.232+08:00] adb getScreenDensity 
[2025-09-01T17:28:43.330+08:00] adb getScreenDensity  end
[2025-09-01T17:28:43.330+08:00] Taking screenshot via adb.takeScreenshot
[2025-09-01T17:28:43.330+08:00] adb takeScreenshot 
[2025-09-01T17:28:43.528+08:00] adb takeScreenshot  end
[2025-09-01T17:28:43.529+08:00] adb.takeScreenshot completed
[2025-09-01T17:28:43.529+08:00] Invalid image buffer detected: not a valid image format
[2025-09-01T17:28:43.529+08:00] Taking screenshot via adb.takeScreenshot failed or was skipped: Error: Screenshot buffer has invalid format: could not find valid image signature
[2025-09-01T17:28:43.529+08:00] Fallback: taking screenshot via shell screencap
[2025-09-01T17:28:43.529+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png
[2025-09-01T17:28:44.148+08:00] adb shell screencap -p  /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png end
[2025-09-01T17:28:44.148+08:00] adb.shell screencap completed
[2025-09-01T17:28:44.148+08:00] Pulling screenshot file from device
[2025-09-01T17:28:44.148+08:00] adb pull /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4w7hzan4xlk.png
[2025-09-01T17:28:44.233+08:00] adb pull /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4w7hzan4xlk.png end
[2025-09-01T17:28:44.233+08:00] adb.pull completed, local path: C:\Users\<USER>\AppData\Local\Temp\midscene-unknown-package-name\4w7hzan4xlk.png
[2025-09-01T17:28:44.234+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png
[2025-09-01T17:28:44.314+08:00] adb shell rm /data/local/tmp/midscene_screenshot_f9e8fa7d-1bdd-47f1-9019-6eec6b284448.png end
[2025-09-01T17:28:44.314+08:00] Resizing screenshot image
[2025-09-01T17:28:44.467+08:00] Image resize completed
[2025-09-01T17:28:44.467+08:00] Converting to base64
[2025-09-01T17:28:44.467+08:00] screenshotBase64 end
[2025-09-01T17:28:44.467+08:00] adb shell wm,size
[2025-09-01T17:28:44.560+08:00] adb shell wm,size end
[2025-09-01T17:28:44.560+08:00] Using Physical size: 1080x2436
[2025-09-01T17:28:44.560+08:00] adb shell dumpsys input | grep SurfaceOrientation
[2025-09-01T17:28:44.657+08:00] ADB error with device ASALE3741B000022 when calling shell: Error: Error executing adbExec. Original error: 'Command 'adb -P 5037 -s ASALE3741B000022 shell 'dumpsys input | grep SurfaceOrientation'' exited with code 1'; Command output: <empty>
[2025-09-01T17:28:44.657+08:00] Failed to get orientation from input, try display
[2025-09-01T17:28:44.657+08:00] adb shell dumpsys display | grep mCurrentOrientation
[2025-09-01T17:28:44.805+08:00] adb shell dumpsys display | grep mCurrentOrientation end
[2025-09-01T17:28:44.805+08:00] Screen orientation (fallback): 0
[2025-09-01T17:28:44.805+08:00] adb getScreenDensity 
[2025-09-01T17:28:44.912+08:00] adb getScreenDensity  end
